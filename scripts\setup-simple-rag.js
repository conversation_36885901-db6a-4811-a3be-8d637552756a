async function setupSimpleRAG() {
  try {
    console.log('🚀 بدء إعداد نظام RAG البسيط...');

    // تحميل خدمة RAG البسيطة
    const { initializeSimpleRAG, searchSimpleRAG, getSimpleRAGStats } = await import('../src/lib/simple-rag-service.js');

    // تهيئة النظام
    await initializeSimpleRAG();

    console.log('✅ تم تهيئة نظام RAG البسيط بنجاح');

    // الحصول على الإحصائيات
    const stats = getSimpleRAGStats();
    console.log('📊 إحصائيات النظام:', stats);

    // اختبار البحث
    console.log('🔍 اختبار البحث...');
    
    const testQueries = [
      'أكثر المنتجات مبيعاً',
      'تفاصيل جدول المنتجات',
      'العلاقات بين الجداول',
      'أمثلة استعلامات SQL',
      'النيات والكيانات'
    ];

    for (const query of testQueries) {
      console.log(`\n🔍 اختبار: "${query}"`);
      const results = await searchSimpleRAG(query, 2);
      
      console.log(`📋 النتائج: ${results.length} مستندات`);
      
      if (results.length > 0) {
        results.forEach((result, index) => {
          console.log(`   ${index + 1}. النقاط: ${result.score}, الصلة: ${result.relevance}`);
          console.log(`      المحتوى: ${result.document.content.substring(0, 80)}...`);
          console.log(`      النوع: ${result.document.metadata.content_type || 'غير محدد'}`);
        });
      }
    }

    console.log('\n🎉 تم إعداد واختبار نظام RAG البسيط بنجاح!');
    console.log('📋 الملخص:');
    console.log(`   - إجمالي المستندات: ${stats.total_documents}`);
    console.log(`   - نوع النظام: ${stats.system_type}`);
    console.log(`   - الحالة: ${stats.status}`);

  } catch (error) {
    console.error('❌ خطأ في إعداد نظام RAG البسيط:', error);
    process.exit(1);
  }
}

// تشغيل الإعداد
if (require.main === module) {
  setupSimpleRAG();
}

module.exports = { setupSimpleRAG };
