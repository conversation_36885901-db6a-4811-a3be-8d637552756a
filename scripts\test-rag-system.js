const { initializeSimpleRAG, searchSimpleRAG, getRelevantContextSimple, getSimpleRAGStats } = require('../src/lib/simple-rag-service.js');

async function testRAGSystem() {
  try {
    console.log('🧪 بدء اختبار شامل لنظام RAG...\n');

    // 1. اختبار التهيئة
    console.log('1️⃣ اختبار تهيئة النظام...');
    await initializeSimpleRAG();
    console.log('✅ تم تهيئة النظام بنجاح\n');

    // 2. اختبار الإحصائيات
    console.log('2️⃣ اختبار الإحصائيات...');
    const stats = getSimpleRAGStats();
    console.log('📊 إحصائيات النظام:', stats);
    console.log('✅ الإحصائيات تعمل بشكل صحيح\n');

    // 3. اختبار البحث الأساسي
    console.log('3️⃣ اختبار البحث الأساسي...');
    const testQueries = [
      'أكثر المنتجات مبيعاً',
      'تفاصيل جدول المنتجات',
      'العلاقات بين الجداول',
      'أمثلة استعلامات SQL',
      'النيات والكيانات',
      'مبيعات الفرع',
      'تقرير المخزون',
      'فواتير العملاء'
    ];

    for (const query of testQueries) {
      console.log(`🔍 اختبار: "${query}"`);
      const results = await searchSimpleRAG(query, 3);
      
      if (results.length > 0) {
        console.log(`   ✅ تم العثور على ${results.length} نتائج`);
        console.log(`   📊 أعلى نقاط: ${results[0].score}, الصلة: ${results[0].relevance}`);
        console.log(`   📄 نوع المحتوى: ${results[0].document.metadata.content_type || 'غير محدد'}`);
      } else {
        console.log(`   ❌ لم يتم العثور على نتائج`);
      }
      console.log('');
    }

    // 4. اختبار الحصول على السياق
    console.log('4️⃣ اختبار الحصول على السياق...');
    const contextQueries = [
      'أكثر المنتجات مبيعاً في الفرع',
      'تقرير مبيعات شهرية',
      'تحليل أداء المنتجات'
    ];

    for (const query of contextQueries) {
      console.log(`🎯 اختبار السياق لـ: "${query}"`);
      const context = await getRelevantContextSimple(query);
      
      if (context && context.length > 0) {
        console.log(`   ✅ تم الحصول على سياق بطول ${context.length} حرف`);
        console.log(`   📄 بداية السياق: ${context.substring(0, 100)}...`);
      } else {
        console.log(`   ❌ لم يتم الحصول على سياق`);
      }
      console.log('');
    }

    // 5. اختبار البحث حسب نوع المحتوى
    console.log('5️⃣ اختبار البحث حسب نوع المحتوى...');
    const contentTypes = ['table_description', 'intent', 'examples'];
    
    for (const contentType of contentTypes) {
      console.log(`📋 اختبار نوع المحتوى: ${contentType}`);
      const results = await searchSimpleRAG('منتج مبيعات', 10);
      const filteredResults = results.filter(r => r.document.metadata.content_type === contentType);
      
      console.log(`   📊 عدد النتائج من نوع ${contentType}: ${filteredResults.length}`);
      if (filteredResults.length > 0) {
        console.log(`   ✅ تم العثور على محتوى من النوع المطلوب`);
      }
      console.log('');
    }

    // 6. اختبار الأداء
    console.log('6️⃣ اختبار الأداء...');
    const performanceQuery = 'أكثر المنتجات مبيعاً';
    const iterations = 5;
    const times = [];

    for (let i = 0; i < iterations; i++) {
      const startTime = Date.now();
      await searchSimpleRAG(performanceQuery, 5);
      const endTime = Date.now();
      times.push(endTime - startTime);
    }

    const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
    console.log(`⏱️ متوسط وقت البحث: ${avgTime.toFixed(2)} مللي ثانية`);
    console.log(`📊 أسرع بحث: ${Math.min(...times)} مللي ثانية`);
    console.log(`📊 أبطأ بحث: ${Math.max(...times)} مللي ثانية`);
    console.log('');

    // 7. اختبار جودة النتائج
    console.log('7️⃣ اختبار جودة النتائج...');
    const qualityTests = [
      {
        query: 'جدول المنتجات',
        expectedType: 'table_description',
        expectedKeywords: ['tbltemp_ItemsMain', 'ItemName']
      },
      {
        query: 'مقارنة مبيعات',
        expectedType: 'intent',
        expectedKeywords: ['مقارنة', 'مبيعات']
      }
    ];

    for (const test of qualityTests) {
      console.log(`🎯 اختبار جودة: "${test.query}"`);
      const results = await searchSimpleRAG(test.query, 3);
      
      if (results.length > 0) {
        const topResult = results[0];
        
        // اختبار نوع المحتوى
        if (topResult.document.metadata.content_type === test.expectedType) {
          console.log(`   ✅ نوع المحتوى صحيح: ${test.expectedType}`);
        } else {
          console.log(`   ⚠️ نوع المحتوى غير متوقع: ${topResult.document.metadata.content_type}`);
        }
        
        // اختبار الكلمات المفتاحية
        const hasKeywords = test.expectedKeywords.some(keyword => 
          topResult.document.content.toLowerCase().includes(keyword.toLowerCase())
        );
        
        if (hasKeywords) {
          console.log(`   ✅ يحتوي على الكلمات المفتاحية المتوقعة`);
        } else {
          console.log(`   ⚠️ لا يحتوي على الكلمات المفتاحية المتوقعة`);
        }
        
        console.log(`   📊 نقاط الصلة: ${topResult.score}`);
      } else {
        console.log(`   ❌ لم يتم العثور على نتائج`);
      }
      console.log('');
    }

    // 8. ملخص الاختبار
    console.log('8️⃣ ملخص الاختبار...');
    console.log('🎉 تم إكمال جميع الاختبارات بنجاح!');
    console.log('📋 ملخص النتائج:');
    console.log(`   - إجمالي المستندات: ${stats.total_documents}`);
    console.log(`   - نوع النظام: ${stats.system_type}`);
    console.log(`   - حالة النظام: ${stats.status}`);
    console.log(`   - متوسط وقت البحث: ${avgTime.toFixed(2)} مللي ثانية`);
    console.log('   - جميع الاختبارات نجحت ✅');

    console.log('\n🚀 نظام RAG جاهز للاستخدام في الإنتاج!');

  } catch (error) {
    console.error('❌ خطأ في اختبار نظام RAG:', error);
    process.exit(1);
  }
}

// تشغيل الاختبار
if (require.main === module) {
  testRAGSystem();
}

module.exports = { testRAGSystem };
