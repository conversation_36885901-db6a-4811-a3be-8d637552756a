const { ChromaClient } = require('chromadb');
const fs = require('fs').promises;
const path = require('path');

// إعداد Chroma Client
let chromaClient = null;
let collection = null;

// ChromaDB سيستخدم نظام embeddings المدمج تلقائياً

// تهيئة Chroma Database
async function initializeChromaRAG() {
  try {
    console.log('🚀 بدء تهيئة Chroma RAG Database...');
    
    // إنشاء Chroma client مع مجلد دائم
    const chromaPath = path.join(process.cwd(), 'chroma_db');
    
    // التأكد من وجود المجلد
    try {
      await fs.access(chromaPath);
    } catch {
      await fs.mkdir(chromaPath, { recursive: true });
      console.log('📁 تم إنشاء مجلد قاعدة البيانات:', chromaPath);
    }
    
    // استخدام in-memory client بدلاً من persistent لتجنب مشاكل الإعداد
    chromaClient = new ChromaClient();

    console.log('✅ تم إنشاء Chroma client');

    // إنشاء أو الحصول على collection
    try {
      collection = await chromaClient.getCollection({
        name: 'sql_knowledge_embeddings'
      });
      console.log('✅ تم العثور على collection موجودة');
    } catch (error) {
      console.log('📝 إنشاء collection جديدة...');
      collection = await chromaClient.createCollection({
        name: 'sql_knowledge_embeddings',
        metadata: {
          description: 'قاعدة معرفة SQL مع ChromaDB embeddings',
          created_at: new Date().toISOString(),
          version: '2.0',
          embedding_model: 'chromadb_default'
        }
      });
      console.log('✅ تم إنشاء collection جديدة');
    }

    console.log('✅ تم تهيئة Chroma RAG Database بنجاح');
  } catch (error) {
    console.error('❌ خطأ في تهيئة Chroma RAG Database:', error);
    throw error;
  }
}

// تقسيم النص إلى chunks
function splitTextIntoChunks(text, maxChunkSize = 1000) {
  const chunks = [];
  const lines = text.split('\n');
  let currentChunk = '';

  for (const line of lines) {
    if (currentChunk.length + line.length > maxChunkSize && currentChunk.length > 0) {
      chunks.push(currentChunk.trim());
      currentChunk = line;
    } else {
      currentChunk += (currentChunk ? '\n' : '') + line;
    }
  }

  if (currentChunk.trim()) {
    chunks.push(currentChunk.trim());
  }

  return chunks;
}

// استخراج metadata من النص
function extractMetadata(text, chunkIndex) {
  const metadata = {
    chunk_index: chunkIndex,
    length: text.length,
    type: 'knowledge_base',
    embedding_model: 'chromadb_default'
  };

  // استخراج نوع المحتوى
  if (text.includes('الجدول')) {
    metadata.content_type = 'table_description';
    
    // استخراج اسم الجدول
    const tableMatch = text.match(/الجدول.*?:\s*(\w+)/);
    if (tableMatch) {
      metadata.table_name = tableMatch[1];
    }
  } else if (text.includes('النية:') || text.includes('intent:')) {
    metadata.content_type = 'intent';
    
    // استخراج اسم النية
    const intentMatch = text.match(/intent\s*:\s*(\w+)/);
    if (intentMatch) {
      metadata.intent_name = intentMatch[1];
    }
  } else if (text.includes('أوصاف الأعمدة:')) {
    metadata.content_type = 'column_descriptions';
  } else if (text.includes('العلاقات بين الجداول:')) {
    metadata.content_type = 'table_relationships';
  } else if (text.includes('أمثلة تطبيقية:')) {
    metadata.content_type = 'examples';
  } else if (text.includes('الكيانات')) {
    metadata.content_type = 'entities';
  }

  // استخراج الجداول المذكورة
  const tableMatches = text.match(/tbltemp_\w+/g);
  if (tableMatches) {
    metadata.mentioned_tables = [...new Set(tableMatches)];
  }

  // استخراج الأعمدة المذكورة
  const columnMatches = text.match(/\b[A-Z][a-zA-Z]*(?:ID|Name|Date|Amount|Quantity|Price)\b/g);
  if (columnMatches) {
    metadata.mentioned_columns = [...new Set(columnMatches)];
  }

  return metadata;
}

// تحميل وفهرسة محتوى ملف rga.txt مع embeddings حقيقية
async function indexKnowledgeBaseWithEmbeddings() {
  try {
    if (!collection) {
      throw new Error('Chroma Database غير مهيأ');
    }

    console.log('📚 بدء فهرسة قاعدة المعرفة مع embeddings...');

    // قراءة ملف rga.txt
    const rgaPath = path.join(process.cwd(), 'src/data/rga.txt');
    const rgaContent = await fs.readFile(rgaPath, 'utf-8');

    console.log(`📄 تم قراءة الملف: ${rgaContent.length} حرف`);

    // تقسيم النص إلى chunks
    const chunks = splitTextIntoChunks(rgaContent, 800);
    console.log(`🔪 تم تقسيم النص إلى ${chunks.length} قطعة`);

    // حذف البيانات الموجودة إن وجدت
    try {
      const existingCount = await collection.count();
      if (existingCount > 0) {
        await collection.delete();
        console.log('🗑️ تم حذف البيانات القديمة');
      }
    } catch (error) {
      console.log('ℹ️ لا توجد بيانات قديمة للحذف');
    }

    // إعداد البيانات للفهرسة
    const documents = [];
    const metadatas = [];
    const ids = [];

    console.log('🔄 إعداد البيانات للفهرسة...');

    // إعداد البيانات لكل chunk
    for (let i = 0; i < chunks.length; i++) {
      const chunk = chunks[i];
      console.log(`📊 معالجة القطعة ${i + 1}/${chunks.length}...`);

      documents.push(chunk);
      metadatas.push(extractMetadata(chunk, i));
      ids.push(`chunk_${i}`);
    }

    console.log('💾 حفظ البيانات في Chroma Database...');

    // إضافة البيانات إلى Chroma (سيقوم ChromaDB بإنشاء embeddings تلقائياً)
    await collection.add({
      documents,
      metadatas,
      ids
    });

    console.log(`✅ تم فهرسة ${chunks.length} قطعة مع embeddings بنجاح`);

    // عرض إحصائيات
    const count = await collection.count();
    console.log(`📊 إجمالي العناصر في قاعدة البيانات: ${count}`);

    // حفظ معلومات الفهرسة
    const indexInfo = {
      indexed_at: new Date().toISOString(),
      total_chunks: count,
      embedding_model: 'chromadb_default',
      status: 'ready',
      database_path: 'in_memory'
    };

    await fs.writeFile(
      path.join(process.cwd(), 'src/data/chroma-index-status.json'),
      JSON.stringify(indexInfo, null, 2)
    );

    console.log('💾 تم حفظ معلومات الفهرسة');

  } catch (error) {
    console.error('❌ خطأ في فهرسة قاعدة المعرفة:', error);
    throw error;
  }
}

// البحث في قاعدة المعرفة باستخدام ChromaDB
async function searchWithEmbeddings(query, limit = 5) {
  try {
    if (!collection) {
      throw new Error('Chroma Database غير مهيأ');
    }

    console.log(`🔍 البحث في ChromaDB: "${query}"`);

    // تنفيذ البحث باستخدام النص مباشرة (ChromaDB سيقوم بإنشاء embeddings تلقائياً)
    const results = await collection.query({
      queryTexts: [query],
      nResults: limit
    });

    console.log(`📋 تم العثور على ${results.documents[0]?.length || 0} نتائج`);

    // تنسيق النتائج
    const formattedResults = [];
    if (results.documents[0] && results.metadatas[0] && results.distances[0]) {
      for (let i = 0; i < results.documents[0].length; i++) {
        formattedResults.push({
          content: results.documents[0][i],
          metadata: results.metadatas[0][i],
          similarity: 1 - (results.distances[0][i] || 0), // تحويل المسافة إلى تشابه
          distance: results.distances[0][i],
          id: results.ids[0]?.[i]
        });
      }
    }

    return formattedResults;
  } catch (error) {
    console.error('❌ خطأ في البحث:', error);
    return [];
  }
}

// الحصول على السياق المناسب للاستعلام باستخدام embeddings
async function getRelevantContextWithEmbeddings(query) {
  try {
    console.log('🎯 الحصول على السياق باستخدام embeddings...');

    const results = await searchWithEmbeddings(query, 5);

    // تكوين السياق
    const context = results
      .map(result => result.content)
      .join('\n\n---\n\n');

    console.log(`✅ تم تكوين سياق بطول ${context.length} حرف من ${results.length} مصادر`);

    return context;
  } catch (error) {
    console.error('❌ خطأ في الحصول على السياق:', error);
    return '';
  }
}

// إحصائيات قاعدة البيانات
async function getChromaStats() {
  try {
    if (!collection) {
      return { error: 'Chroma Database غير مهيأ' };
    }

    const count = await collection.count();
    
    // قراءة معلومات الفهرسة
    try {
      const statusPath = path.join(process.cwd(), 'src/data/chroma-index-status.json');
      const statusData = await fs.readFile(statusPath, 'utf-8');
      const status = JSON.parse(statusData);
      
      return {
        total_documents: count,
        indexed_at: status.indexed_at,
        embedding_model: status.embedding_model,
        database_path: status.database_path,
        status: 'ready',
        type: 'Chroma_with_Embeddings'
      };
    } catch (error) {
      return {
        total_documents: count,
        status: 'ready',
        type: 'Chroma_with_Embeddings'
      };
    }
  } catch (error) {
    console.error('❌ خطأ في الحصول على إحصائيات Chroma:', error);
    return { error: 'فشل في الحصول على الإحصائيات' };
  }
}

module.exports = {
  initializeChromaRAG,
  indexKnowledgeBaseWithEmbeddings,
  searchWithEmbeddings,
  getRelevantContextWithEmbeddings,
  getChromaStats
};
