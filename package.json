{"name": "sql-ai-agent", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test:db": "node scripts/test-connection.js", "setup": "npm install && npm run test:db", "setup:vector": "node scripts/setup-vector-db.js", "setup:rag": "node scripts/setup-rag.js", "setup:simple-rag": "node scripts/setup-simple-rag.js", "test:rag": "node scripts/test-rag-system.js", "setup:chroma": "node scripts/setup-chroma-embeddings.js", "setup:chroma-real": "node scripts/setup-chroma-real.js", "clean": "rm -rf .next node_modules/.cache", "type-check": "tsc --noEmit"}, "dependencies": {"@chroma-core/default-embed": "^0.1.8", "@huggingface/inference": "^4.5.3", "@langchain/community": "^0.3.49", "@langchain/openai": "^0.6.3", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-slot": "^1.1.1", "better-sqlite3": "^11.10.0", "chromadb": "^3.0.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "faiss-node": "^0.5.1", "groq-sdk": "^0.29.0", "langchain": "^0.3.30", "lucide-react": "^0.468.0", "mssql": "^11.0.1", "next": "15.4.3", "openai": "^4.73.1", "react": "19.1.0", "react-dom": "19.1.0", "recharts": "^2.15.0", "sqlite3": "^5.1.7", "tailwind-merge": "^2.5.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/mssql": "^9.1.5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.3", "tailwindcss": "^4", "typescript": "^5"}}