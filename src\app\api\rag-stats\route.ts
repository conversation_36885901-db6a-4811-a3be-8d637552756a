import { NextRequest, NextResponse } from 'next/server';
import { getRAGSystemStats } from '@/lib/ai-service';

export async function GET(request: NextRequest) {
  try {
    console.log('📊 طلب إحصائيات نظام RAG...');
    
    const stats = await getRAGSystemStats();
    
    console.log('✅ تم الحصول على إحصائيات RAG:', stats);
    
    return NextResponse.json(stats);
  } catch (error) {
    console.error('❌ خطأ في الحصول على إحصائيات RAG:', error);
    
    return NextResponse.json(
      { 
        error: 'فشل في الحصول على إحصائيات نظام RAG',
        details: error instanceof Error ? error.message : 'خطأ غير معروف'
      },
      { status: 500 }
    );
  }
}
