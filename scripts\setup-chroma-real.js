async function setupChromaReal() {
  try {
    console.log('🚀 بدء إعداد نظام Chroma Vector Database الحقيقي...');

    // تحميل خدمة Chroma Vector
    const { 
      initializeChromaVector, 
      indexChromaKnowledgeBase, 
      searchChromaKnowledge, 
      getChromaVectorStats,
      stopChromaServer
    } = await import('../src/lib/chroma-vector-service.js');

    console.log('✅ تم تحميل خدمة Chroma Vector');

    // 1. تهيئة قاعدة البيانات
    console.log('\n1️⃣ تهيئة Chroma Vector Database...');
    await initializeChromaVector();

    // 2. فهرسة قاعدة المعرفة
    console.log('\n2️⃣ فهرسة قاعدة المعرفة في Chroma...');
    await indexChromaKnowledgeBase();

    // 3. الحصول على الإحصائيات
    console.log('\n3️⃣ الحصول على إحصائيات Chroma...');
    const stats = await getChromaVectorStats();
    console.log('📊 إحصائيات Chroma Vector:', JSON.stringify(stats, null, 2));

    // 4. اختبار البحث
    console.log('\n4️⃣ اختبار البحث في Chroma Vector...');
    
    const testQueries = [
      'أكثر المنتجات مبيعاً',
      'تفاصيل جدول المنتجات',
      'العلاقات بين الجداول',
      'أمثلة استعلامات SQL',
      'مبيعات الفرع'
    ];

    for (const query of testQueries) {
      console.log(`\n🔍 اختبار: "${query}"`);
      const results = await searchChromaKnowledge(query, 3);
      
      if (results.length > 0) {
        console.log(`   ✅ تم العثور على ${results.length} نتائج`);
        console.log(`   📊 أعلى تشابه: ${(results[0].similarity * 100).toFixed(1)}%`);
        console.log(`   📄 نوع المحتوى: ${results[0].metadata.content_type || 'غير محدد'}`);
        console.log(`   📏 المسافة: ${results[0].distance?.toFixed(4) || 'غير محدد'}`);
        console.log(`   📝 بداية المحتوى: ${results[0].content.substring(0, 80)}...`);
        
        // عرض metadata إضافية
        if (results[0].metadata.mentioned_tables) {
          console.log(`   🗂️ الجداول المذكورة: ${results[0].metadata.mentioned_tables.join(', ')}`);
        }
        if (results[0].metadata.mentioned_columns) {
          console.log(`   📋 الأعمدة المذكورة: ${results[0].metadata.mentioned_columns.slice(0, 3).join(', ')}`);
        }
      } else {
        console.log(`   ❌ لم يتم العثور على نتائج`);
      }
    }

    // 5. اختبار الأداء
    console.log('\n5️⃣ اختبار أداء Chroma Vector...');
    const performanceQuery = 'أكثر المنتجات مبيعاً';
    const iterations = 3;
    const times = [];

    for (let i = 0; i < iterations; i++) {
      const startTime = Date.now();
      await searchChromaKnowledge(performanceQuery, 5);
      const endTime = Date.now();
      times.push(endTime - startTime);
      console.log(`   ⏱️ التكرار ${i + 1}: ${endTime - startTime} مللي ثانية`);
    }

    const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
    console.log(`   📊 متوسط الوقت: ${avgTime.toFixed(2)} مللي ثانية`);

    // 6. اختبار جودة النتائج
    console.log('\n6️⃣ اختبار جودة النتائج...');
    const qualityTests = [
      {
        query: 'جدول المنتجات',
        expectedType: 'table_description',
        expectedKeywords: ['tbltemp_ItemsMain', 'ItemName']
      },
      {
        query: 'مقارنة مبيعات',
        expectedType: 'intent',
        expectedKeywords: ['مقارنة', 'مبيعات']
      }
    ];

    for (const test of qualityTests) {
      console.log(`\n🎯 اختبار جودة: "${test.query}"`);
      const results = await searchChromaKnowledge(test.query, 3);
      
      if (results.length > 0) {
        const topResult = results[0];
        
        // اختبار نوع المحتوى
        if (topResult.metadata.content_type === test.expectedType) {
          console.log(`   ✅ نوع المحتوى صحيح: ${test.expectedType}`);
        } else {
          console.log(`   ⚠️ نوع المحتوى غير متوقع: ${topResult.metadata.content_type}`);
        }
        
        // اختبار الكلمات المفتاحية
        const hasKeywords = test.expectedKeywords.some(keyword => 
          topResult.content.toLowerCase().includes(keyword.toLowerCase())
        );
        
        if (hasKeywords) {
          console.log(`   ✅ يحتوي على الكلمات المفتاحية المتوقعة`);
        } else {
          console.log(`   ⚠️ لا يحتوي على الكلمات المفتاحية المتوقعة`);
        }
        
        console.log(`   📊 نقاط التشابه: ${(topResult.similarity * 100).toFixed(1)}%`);
      } else {
        console.log(`   ❌ لم يتم العثور على نتائج`);
      }
    }

    // 7. فحص قاعدة البيانات
    console.log('\n7️⃣ فحص قاعدة البيانات...');
    const fs = require('fs').promises;
    const path = require('path');
    
    const dbPath = path.join(process.cwd(), 'chroma_database');
    try {
      const dbStats = await fs.stat(dbPath);
      console.log(`   📁 مجلد قاعدة البيانات موجود: ${dbPath}`);
      console.log(`   📊 تاريخ الإنشاء: ${dbStats.birthtime.toLocaleString('ar-SA')}`);
      console.log(`   📊 آخر تعديل: ${dbStats.mtime.toLocaleString('ar-SA')}`);
      
      // فحص محتويات المجلد
      const files = await fs.readdir(dbPath);
      console.log(`   📄 عدد الملفات: ${files.length}`);
      if (files.length > 0) {
        console.log(`   📋 الملفات: ${files.slice(0, 5).join(', ')}${files.length > 5 ? '...' : ''}`);
      }
    } catch (error) {
      console.log(`   ⚠️ مجلد قاعدة البيانات غير موجود أو غير قابل للوصول`);
    }

    // 8. ملخص النتائج
    console.log('\n8️⃣ ملخص الإعداد...');
    console.log('🎉 تم إعداد Chroma Vector Database بنجاح!');
    console.log('📋 الملخص:');
    console.log(`   - إجمالي المستندات: ${stats.total_documents}`);
    console.log(`   - نوع Embedding: ${stats.embedding_type || 'chroma_local'}`);
    console.log(`   - مسار قاعدة البيانات: ${stats.database_path || './chroma_database'}`);
    console.log(`   - نوع النظام: ${stats.type}`);
    console.log(`   - متوسط وقت البحث: ${avgTime.toFixed(2)} مللي ثانية`);
    console.log(`   - حالة النظام: ${stats.status}`);

    console.log('\n✅ نظام Chroma Vector Database جاهز للاستخدام!');
    console.log('📁 قاعدة البيانات محفوظة في مجلد: ./chroma_database');
    console.log('🔗 يمكن الآن ربطه مع النموذج اللغوي لتوليد استعلامات SQL');

    // إيقاف خادم Chroma إذا كان يعمل
    await stopChromaServer();

  } catch (error) {
    console.error('❌ خطأ في إعداد Chroma Vector Database:', error);
    process.exit(1);
  }
}

// تشغيل الإعداد
if (require.main === module) {
  setupChromaReal();
}

module.exports = { setupChromaReal };
