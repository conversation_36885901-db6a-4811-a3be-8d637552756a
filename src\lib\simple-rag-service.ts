import fs from 'fs/promises';
import path from 'path';

// نظام RAG بسيط بدون OpenAI embeddings
interface Document {
  id: string;
  content: string;
  metadata: Record<string, any>;
  keywords: string[];
}

interface SearchResult {
  document: Document;
  score: number;
  relevance: string;
}

class SimpleRAGService {
  private documents: Document[] = [];
  private isInitialized = false;

  // تهيئة النظام
  async initialize(): Promise<void> {
    try {
      console.log('🚀 بدء تهيئة نظام RAG البسيط...');
      
      // قراءة ملف rga.txt
      const rgaPath = path.join(process.cwd(), 'src/data/rga.txt');
      const rgaContent = await fs.readFile(rgaPath, 'utf-8');

      console.log(`📄 تم قراءة الملف: ${rgaContent.length} حرف`);

      // تقسيم النص إلى chunks
      const chunks = this.splitTextIntoChunks(rgaContent, 1000);
      console.log(`🔪 تم تقسيم النص إلى ${chunks.length} قطعة`);

      // إنشاء Documents
      this.documents = chunks.map((chunk, index) => ({
        id: `chunk_${index}`,
        content: chunk,
        metadata: this.extractMetadata(chunk, index),
        keywords: this.extractKeywords(chunk)
      }));

      this.isInitialized = true;
      console.log(`✅ تم تهيئة ${this.documents.length} مستند بنجاح`);

      // حفظ معلومات التهيئة
      await this.saveInitializationInfo();

    } catch (error) {
      console.error('❌ خطأ في تهيئة نظام RAG:', error);
      throw error;
    }
  }

  // تقسيم النص إلى chunks
  private splitTextIntoChunks(text: string, maxChunkSize: number = 1000): string[] {
    const chunks: string[] = [];
    const lines = text.split('\n');
    let currentChunk = '';

    for (const line of lines) {
      if (currentChunk.length + line.length > maxChunkSize && currentChunk.length > 0) {
        chunks.push(currentChunk.trim());
        currentChunk = line;
      } else {
        currentChunk += (currentChunk ? '\n' : '') + line;
      }
    }

    if (currentChunk.trim()) {
      chunks.push(currentChunk.trim());
    }

    return chunks;
  }

  // استخراج metadata من النص
  private extractMetadata(text: string, chunkIndex: number): Record<string, any> {
    const metadata: Record<string, any> = {
      chunk_index: chunkIndex,
      length: text.length,
      type: 'knowledge_base'
    };

    // استخراج نوع المحتوى
    if (text.includes('الجدول')) {
      metadata.content_type = 'table_description';
      
      // استخراج اسم الجدول
      const tableMatch = text.match(/الجدول.*?:\s*(\w+)/);
      if (tableMatch) {
        metadata.table_name = tableMatch[1];
      }
    } else if (text.includes('النية:') || text.includes('intent:')) {
      metadata.content_type = 'intent';
      
      // استخراج اسم النية
      const intentMatch = text.match(/intent\s*:\s*(\w+)/);
      if (intentMatch) {
        metadata.intent_name = intentMatch[1];
      }
    } else if (text.includes('أوصاف الأعمدة:')) {
      metadata.content_type = 'column_descriptions';
    } else if (text.includes('العلاقات بين الجداول:')) {
      metadata.content_type = 'table_relationships';
    } else if (text.includes('أمثلة تطبيقية:')) {
      metadata.content_type = 'examples';
    } else if (text.includes('الكيانات')) {
      metadata.content_type = 'entities';
    }

    // استخراج الجداول المذكورة
    const tableMatches = text.match(/tbltemp_\w+/g);
    if (tableMatches) {
      metadata.mentioned_tables = [...new Set(tableMatches)];
    }

    // استخراج الأعمدة المذكورة
    const columnMatches = text.match(/\b[A-Z][a-zA-Z]*(?:ID|Name|Date|Amount|Quantity|Price)\b/g);
    if (columnMatches) {
      metadata.mentioned_columns = [...new Set(columnMatches)];
    }

    return metadata;
  }

  // استخراج الكلمات المفتاحية
  private extractKeywords(text: string): string[] {
    const keywords: string[] = [];
    
    // كلمات مفتاحية مهمة
    const importantTerms = [
      'منتج', 'مبيعات', 'عميل', 'فاتورة', 'مخزون', 'كمية', 'سعر', 'مبلغ',
      'تاريخ', 'فرع', 'مورد', 'موزع', 'باركود', 'تصنيف', 'فئة',
      'ItemName', 'ClientName', 'Amount', 'Quantity', 'UnitPrice', 'TheDate',
      'BranchName', 'DocumentName', 'CategoryName', 'DistributorName',
      'tbltemp_ItemsMain', 'tbltemp_Inv_MainInvoice'
    ];

    const textLower = text.toLowerCase();
    for (const term of importantTerms) {
      if (textLower.includes(term.toLowerCase())) {
        keywords.push(term);
      }
    }

    return [...new Set(keywords)];
  }

  // البحث في المستندات
  async search(query: string, limit: number = 5): Promise<SearchResult[]> {
    if (!this.isInitialized) {
      throw new Error('نظام RAG غير مهيأ');
    }

    console.log(`🔍 البحث عن: "${query}"`);

    const queryLower = query.toLowerCase();
    const queryKeywords = this.extractKeywords(query);

    // حساب النقاط لكل مستند
    const scoredDocuments = this.documents.map(doc => {
      let score = 0;

      // نقاط التطابق النصي المباشر
      const contentLower = doc.content.toLowerCase();
      const directMatches = queryLower.split(' ').filter(word => 
        word.length > 2 && contentLower.includes(word)
      );
      score += directMatches.length * 2;

      // نقاط تطابق الكلمات المفتاحية
      const keywordMatches = queryKeywords.filter(keyword => 
        doc.keywords.some(docKeyword => 
          docKeyword.toLowerCase().includes(keyword.toLowerCase()) ||
          keyword.toLowerCase().includes(docKeyword.toLowerCase())
        )
      );
      score += keywordMatches.length * 3;

      // نقاط إضافية لأنواع المحتوى المهمة
      if (query.includes('منتج') && doc.metadata.content_type === 'table_description') {
        score += 2;
      }
      if (query.includes('استعلام') && doc.metadata.content_type === 'examples') {
        score += 2;
      }
      if (query.includes('نية') && doc.metadata.content_type === 'intent') {
        score += 2;
      }

      // تحديد مستوى الصلة
      let relevance = 'منخفض';
      if (score >= 5) relevance = 'عالي';
      else if (score >= 3) relevance = 'متوسط';

      return {
        document: doc,
        score,
        relevance
      };
    });

    // ترتيب النتائج حسب النقاط
    const sortedResults = scoredDocuments
      .filter(result => result.score > 0)
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);

    console.log(`📋 تم العثور على ${sortedResults.length} نتائج ذات صلة`);

    return sortedResults;
  }

  // البحث حسب نوع المحتوى
  async searchByContentType(query: string, contentType: string, limit: number = 3): Promise<SearchResult[]> {
    const allResults = await this.search(query, 20);
    return allResults
      .filter(result => result.document.metadata.content_type === contentType)
      .slice(0, limit);
  }

  // الحصول على السياق المناسب للاستعلام
  async getRelevantContext(query: string): Promise<string> {
    try {
      console.log('🎯 الحصول على السياق المناسب للاستعلام...');

      // البحث في أنواع مختلفة من المحتوى
      const [
        tableResults,
        intentResults,
        exampleResults,
        generalResults
      ] = await Promise.all([
        this.searchByContentType(query, 'table_description', 2),
        this.searchByContentType(query, 'intent', 2),
        this.searchByContentType(query, 'examples', 2),
        this.search(query, 3)
      ]);

      // دمج النتائج وإزالة التكرار
      const allResults = [...tableResults, ...intentResults, ...exampleResults, ...generalResults];
      const uniqueResults = allResults.filter((result, index, self) => 
        index === self.findIndex(r => r.document.id === result.document.id)
      );

      // ترتيب حسب النقاط
      uniqueResults.sort((a, b) => b.score - a.score);

      // أخذ أفضل النتائج
      const topResults = uniqueResults.slice(0, 5);

      // تكوين السياق
      const context = topResults
        .map(result => result.document.content)
        .join('\n\n---\n\n');

      console.log(`✅ تم تكوين سياق بطول ${context.length} حرف من ${topResults.length} مصادر`);

      return context;
    } catch (error) {
      console.error('❌ خطأ في الحصول على السياق:', error);
      return '';
    }
  }

  // حفظ معلومات التهيئة
  private async saveInitializationInfo(): Promise<void> {
    const info = {
      initialized_at: new Date().toISOString(),
      total_documents: this.documents.length,
      system_type: 'Simple_RAG',
      status: 'ready'
    };

    await fs.writeFile(
      path.join(process.cwd(), 'src/data/simple-rag-status.json'),
      JSON.stringify(info, null, 2)
    );
  }

  // إحصائيات النظام
  getStats(): any {
    return {
      total_documents: this.documents.length,
      is_initialized: this.isInitialized,
      system_type: 'Simple_RAG',
      status: this.isInitialized ? 'ready' : 'not_initialized'
    };
  }
}

// إنشاء instance واحد
const simpleRAG = new SimpleRAGService();

// تصدير الدوال
export async function initializeSimpleRAG(): Promise<void> {
  return simpleRAG.initialize();
}

export async function searchSimpleRAG(query: string, limit: number = 5): Promise<SearchResult[]> {
  return simpleRAG.search(query, limit);
}

export async function getRelevantContextSimple(query: string): Promise<string> {
  return simpleRAG.getRelevantContext(query);
}

export function getSimpleRAGStats(): any {
  return simpleRAG.getStats();
}
