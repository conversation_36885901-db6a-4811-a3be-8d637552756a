import { FaissStore } from '@langchain/community/vectorstores/faiss';
import { RecursiveCharacterTextSplitter } from 'langchain/text_splitter';
import { Document } from 'langchain/document';
import fs from 'fs/promises';
import path from 'path';

// نظام embeddings محلي متطور
class AdvancedLocalEmbeddings {
  private dimension = 512; // حجم الـ embedding

  async embedDocuments(texts: string[]): Promise<number[][]> {
    return texts.map(text => this.createAdvancedEmbedding(text));
  }

  async embedQuery(text: string): Promise<number[]> {
    return this.createAdvancedEmbedding(text);
  }

  private createAdvancedEmbedding(text: string): number[] {
    const embedding = new Array(this.dimension).fill(0);
    
    // تنظيف النص
    const cleanText = text.toLowerCase().replace(/[^\u0600-\u06FF\u0750-\u077F\w\s]/g, ' ');
    const words = cleanText.split(/\s+/).filter(word => word.length > 1);
    
    // المصطلحات المهمة مع أوزان
    const importantTerms = {
      // مصطلحات قواعد البيانات
      'منتج': 10, 'مبيعات': 10, 'عميل': 8, 'فاتورة': 8, 'مخزون': 7,
      'كمية': 6, 'سعر': 6, 'مبلغ': 6, 'تاريخ': 5, 'فرع': 5,
      'مورد': 5, 'موزع': 5, 'باركود': 4, 'تصنيف': 4, 'فئة': 4,
      
      // أسماء الأعمدة
      'ItemName': 9, 'ClientName': 8, 'Amount': 8, 'Quantity': 7,
      'UnitPrice': 7, 'TheDate': 6, 'BranchName': 6, 'DocumentName': 6,
      'CategoryName': 5, 'DistributorName': 5, 'SupplierName': 5,
      
      // أسماء الجداول
      'tbltemp_ItemsMain': 15, 'tbltemp_Inv_MainInvoice': 15,
      'tbltemp_Clients': 10, 'tbltemp_Suppliers': 10,
      
      // مصطلحات تقنية
      'الجدول': 12, 'النية': 10, 'intent': 10, 'entity': 8,
      'استعلام': 9, 'query': 9, 'sql': 12, 'select': 8,
      'where': 6, 'join': 7, 'group': 6, 'order': 5,
      
      // مصطلحات تجارية
      'أكثر': 7, 'أقل': 6, 'مقارنة': 8, 'تحليل': 9,
      'تقرير': 8, 'إحصائيات': 7, 'نتائج': 6
    };

    // حساب التكرارات
    const wordCounts = new Map<string, number>();
    words.forEach(word => {
      wordCounts.set(word, (wordCounts.get(word) || 0) + 1);
    });

    // إنشاء embedding بناءً على الكلمات والأوزان
    let totalWeight = 0;
    
    words.forEach((word, wordIndex) => {
      const baseWeight = importantTerms[word] || 1;
      const frequency = wordCounts.get(word) || 1;
      const weight = baseWeight * Math.log(frequency + 1);
      totalWeight += weight;
      
      // توزيع الوزن على عدة مواضع في الـ embedding
      for (let i = 0; i < 3; i++) {
        const hash1 = this.simpleHash(word + i.toString());
        const hash2 = this.simpleHash(word + (i + 1).toString());
        
        const index1 = Math.abs(hash1) % this.dimension;
        const index2 = Math.abs(hash2) % this.dimension;
        
        embedding[index1] += weight * 0.6;
        embedding[index2] += weight * 0.4;
      }
      
      // إضافة معلومات موضعية
      const positionWeight = 1 / (wordIndex + 1);
      const posIndex = (wordIndex * 7) % this.dimension;
      embedding[posIndex] += weight * positionWeight * 0.3;
    });

    // تطبيع الـ vector
    const magnitude = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
    const normalizedEmbedding = embedding.map(val => magnitude > 0 ? val / magnitude : 0);
    
    // إضافة ضوضاء صغيرة لتجنب التشابه المطلق
    return normalizedEmbedding.map(val => val + (Math.random() - 0.5) * 0.001);
  }

  private simpleHash(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // تحويل إلى 32bit integer
    }
    return hash;
  }
}

// إعداد FAISS Store
let faissStore: FaissStore | null = null;
let embeddings: AdvancedLocalEmbeddings | null = null;

// تهيئة نظام FAISS RAG
export async function initializeFaissRAG(): Promise<void> {
  try {
    console.log('🚀 بدء تهيئة FAISS RAG Database...');
    
    // إنشاء Advanced Local Embeddings
    embeddings = new AdvancedLocalEmbeddings();
    
    // إنشاء مجلد قاعدة البيانات
    const dbPath = path.join(process.cwd(), 'faiss_database');
    try {
      await fs.access(dbPath);
    } catch {
      await fs.mkdir(dbPath, { recursive: true });
      console.log('📁 تم إنشاء مجلد قاعدة البيانات:', dbPath);
    }

    console.log('✅ تم تهيئة FAISS RAG بنجاح');
  } catch (error) {
    console.error('❌ خطأ في تهيئة FAISS RAG:', error);
    throw error;
  }
}

// استخراج metadata متقدم من النص
function extractAdvancedMetadata(text: string, chunkIndex: number): Record<string, any> {
  const metadata: Record<string, any> = {
    chunk_index: chunkIndex,
    length: text.length,
    type: 'knowledge_base',
    embedding_type: 'faiss_advanced',
    word_count: text.split(/\s+/).length
  };

  // استخراج نوع المحتوى بدقة أكبر
  if (text.includes('الجدول') || text.includes('Table')) {
    metadata.content_type = 'table_description';
    
    // استخراج اسم الجدول
    const tableMatches = text.match(/(?:الجدول|Table).*?:\s*(\w+)/gi);
    if (tableMatches) {
      metadata.table_names = tableMatches.map(match => 
        match.replace(/(?:الجدول|Table).*?:\s*/gi, '').trim()
      );
    }
  } else if (text.includes('النية:') || text.includes('intent:')) {
    metadata.content_type = 'intent';
    
    // استخراج أسماء النيات
    const intentMatches = text.match(/(?:النية|intent)\s*:\s*(\w+)/gi);
    if (intentMatches) {
      metadata.intent_names = intentMatches.map(match => 
        match.replace(/(?:النية|intent)\s*:\s*/gi, '').trim()
      );
    }
  } else if (text.includes('أوصاف الأعمدة') || text.includes('Columns')) {
    metadata.content_type = 'column_descriptions';
  } else if (text.includes('العلاقات بين الجداول') || text.includes('Relationships')) {
    metadata.content_type = 'table_relationships';
  } else if (text.includes('أمثلة تطبيقية') || text.includes('Examples')) {
    metadata.content_type = 'examples';
  } else if (text.includes('الكيانات') || text.includes('Entities')) {
    metadata.content_type = 'entities';
  } else if (text.includes('SQL') || text.includes('SELECT')) {
    metadata.content_type = 'sql_examples';
  }

  // استخراج الجداول المذكورة
  const tableMatches = text.match(/tbltemp_\w+/g);
  if (tableMatches) {
    metadata.mentioned_tables = [...new Set(tableMatches)];
    metadata.table_count = metadata.mentioned_tables.length;
  }

  // استخراج الأعمدة المذكورة
  const columnMatches = text.match(/\b[A-Z][a-zA-Z]*(?:ID|Name|Date|Amount|Quantity|Price|Code|Number)\b/g);
  if (columnMatches) {
    metadata.mentioned_columns = [...new Set(columnMatches)];
    metadata.column_count = metadata.mentioned_columns.length;
  }

  // استخراج الكلمات المفتاحية المهمة
  const keywords = [];
  const importantWords = [
    'منتج', 'مبيعات', 'عميل', 'فاتورة', 'مخزون', 'كمية', 'سعر', 'مبلغ',
    'تاريخ', 'فرع', 'مورد', 'موزع', 'باركود', 'تصنيف', 'فئة', 'استعلام',
    'تقرير', 'تحليل', 'مقارنة', 'إحصائيات'
  ];
  
  const textLower = text.toLowerCase();
  importantWords.forEach(word => {
    if (textLower.includes(word)) {
      keywords.push(word);
    }
  });
  
  if (keywords.length > 0) {
    metadata.keywords = keywords;
    metadata.keyword_count = keywords.length;
  }

  // حساب درجة الأهمية
  let importance = 1;
  if (metadata.content_type === 'table_description') importance += 3;
  if (metadata.content_type === 'intent') importance += 2;
  if (metadata.content_type === 'examples') importance += 2;
  if (metadata.mentioned_tables?.length > 0) importance += metadata.mentioned_tables.length;
  if (metadata.mentioned_columns?.length > 0) importance += metadata.mentioned_columns.length * 0.5;
  if (metadata.keywords?.length > 0) importance += metadata.keywords.length * 0.3;
  
  metadata.importance_score = importance;

  return metadata;
}

// تحميل وفهرسة محتوى ملف rga.txt في FAISS
export async function indexFaissKnowledgeBase(): Promise<void> {
  try {
    if (!embeddings) {
      throw new Error('FAISS RAG غير مهيأ');
    }

    console.log('📚 بدء فهرسة قاعدة المعرفة في FAISS...');

    // قراءة ملف rga.txt
    const rgaPath = path.join(process.cwd(), 'src/data/rga.txt');
    const rgaContent = await fs.readFile(rgaPath, 'utf-8');

    console.log(`📄 تم قراءة الملف: ${rgaContent.length} حرف`);

    // إنشاء Text Splitter متقدم
    const textSplitter = new RecursiveCharacterTextSplitter({
      chunkSize: 1200,
      chunkOverlap: 300,
      separators: ['\n\n', '\n', '。', '؟', '!', ':', ';', '.', '?', '!']
    });

    // تقسيم النص إلى chunks
    const chunks = await textSplitter.splitText(rgaContent);
    console.log(`🔪 تم تقسيم النص إلى ${chunks.length} قطعة`);

    // إنشاء Documents مع metadata متقدم
    const documents = chunks.map((chunk, index) => {
      const metadata = extractAdvancedMetadata(chunk, index);
      return new Document({
        pageContent: chunk,
        metadata
      });
    });

    console.log('🔄 إنشاء embeddings وبناء FAISS index...');

    // إنشاء FAISS Store من Documents
    faissStore = await FaissStore.fromDocuments(documents, embeddings);

    console.log(`✅ تم إنشاء FAISS index مع ${chunks.length} قطعة`);

    // حفظ FAISS index إلى القرص
    const dbPath = path.join(process.cwd(), 'faiss_database');
    await faissStore.save(dbPath);

    console.log(`💾 تم حفظ FAISS index في: ${dbPath}`);

    // حفظ معلومات الفهرسة
    const indexInfo = {
      indexed_at: new Date().toISOString(),
      total_chunks: chunks.length,
      embedding_type: 'faiss_advanced',
      embedding_dimension: 512,
      database_path: dbPath,
      status: 'ready',
      file_source: 'rga.txt',
      file_size: rgaContent.length
    };

    await fs.writeFile(
      path.join(process.cwd(), 'src/data/faiss-index-status.json'),
      JSON.stringify(indexInfo, null, 2)
    );

    console.log('💾 تم حفظ معلومات فهرسة FAISS');

  } catch (error) {
    console.error('❌ خطأ في فهرسة قاعدة المعرفة في FAISS:', error);
    throw error;
  }
}

// تحميل FAISS index من القرص
export async function loadFaissIndex(): Promise<void> {
  try {
    if (!embeddings) {
      await initializeFaissRAG();
    }

    const dbPath = path.join(process.cwd(), 'faiss_database');
    
    // التحقق من وجود الفهرس
    try {
      await fs.access(dbPath);
      console.log('📂 تحميل FAISS index من القرص...');
      faissStore = await FaissStore.load(dbPath, embeddings!);
      console.log('✅ تم تحميل FAISS index بنجاح');
    } catch (error) {
      console.log('⚠️ لم يتم العثور على FAISS index، يجب إنشاؤه أولاً');
      throw new Error('FAISS index غير موجود، يرجى تشغيل الفهرسة أولاً');
    }
  } catch (error) {
    console.error('❌ خطأ في تحميل FAISS index:', error);
    throw error;
  }
}

// البحث في FAISS Store
export async function searchFaissKnowledge(
  query: string, 
  limit: number = 5,
  scoreThreshold: number = 0.1
): Promise<any[]> {
  try {
    if (!faissStore) {
      await loadFaissIndex();
    }

    if (!faissStore) {
      throw new Error('FAISS Store غير متاح');
    }

    console.log(`🔍 البحث في FAISS: "${query}"`);

    // تنفيذ البحث مع النقاط
    const results = await faissStore.similaritySearchWithScore(query, limit);

    console.log(`📋 تم العثور على ${results.length} نتائج في FAISS`);

    // تنسيق النتائج مع فلترة حسب النقاط
    const formattedResults = results
      .filter(result => result[1] >= scoreThreshold) // فلترة النتائج الضعيفة
      .map((result, index) => ({
        content: result[0].pageContent,
        metadata: result[0].metadata,
        similarity: result[1], // FAISS يعطي similarity مباشرة
        score: result[1],
        id: `faiss_result_${index}`,
        rank: index + 1
      }))
      .sort((a, b) => b.similarity - a.similarity); // ترتيب حسب التشابه

    console.log(`✅ تم تنسيق ${formattedResults.length} نتائج عالية الجودة`);

    return formattedResults;
  } catch (error) {
    console.error('❌ خطأ في البحث في FAISS:', error);
    return [];
  }
}

// الحصول على السياق المناسب للنموذج اللغوي من FAISS
export async function getFaissContextForLLM(query: string): Promise<string> {
  try {
    console.log('🎯 الحصول على السياق من FAISS للنموذج اللغوي...');

    // البحث المتقدم
    const results = await searchFaissKnowledge(query, 8, 0.15);

    if (results.length === 0) {
      console.log('⚠️ لم يتم العثور على نتائج مناسبة');
      return '';
    }

    // تجميع النتائج حسب نوع المحتوى
    const groupedResults = {
      table_description: [],
      intent: [],
      examples: [],
      sql_examples: [],
      others: []
    };

    results.forEach(result => {
      const type = result.metadata.content_type || 'others';
      if (groupedResults[type]) {
        groupedResults[type].push(result);
      } else {
        groupedResults.others.push(result);
      }
    });

    // تكوين السياق المنظم للـ LLM
    let context = '';
    
    // إضافة وصف الجداول أولاً
    if (groupedResults.table_description.length > 0) {
      context += '[معلومات الجداول]\n';
      context += groupedResults.table_description
        .slice(0, 2)
        .map(r => r.content)
        .join('\n\n');
      context += '\n\n---\n\n';
    }

    // إضافة النيات
    if (groupedResults.intent.length > 0) {
      context += '[النيات والمقاصد]\n';
      context += groupedResults.intent
        .slice(0, 2)
        .map(r => r.content)
        .join('\n\n');
      context += '\n\n---\n\n';
    }

    // إضافة أمثلة SQL
    if (groupedResults.sql_examples.length > 0) {
      context += '[أمثلة SQL]\n';
      context += groupedResults.sql_examples
        .slice(0, 1)
        .map(r => r.content)
        .join('\n\n');
      context += '\n\n---\n\n';
    }

    // إضافة أمثلة عامة
    if (groupedResults.examples.length > 0) {
      context += '[أمثلة تطبيقية]\n';
      context += groupedResults.examples
        .slice(0, 1)
        .map(r => r.content)
        .join('\n\n');
      context += '\n\n---\n\n';
    }

    // إضافة معلومات أخرى مهمة
    const otherImportant = [...groupedResults.others]
      .sort((a, b) => (b.metadata.importance_score || 1) - (a.metadata.importance_score || 1))
      .slice(0, 2);

    if (otherImportant.length > 0) {
      context += '[معلومات إضافية]\n';
      context += otherImportant
        .map(r => r.content)
        .join('\n\n');
    }

    // تنظيف السياق
    context = context.trim().replace(/\n{3,}/g, '\n\n');

    console.log(`✅ تم تكوين سياق FAISS للـ LLM بطول ${context.length} حرف من ${results.length} مصادر`);

    return context;
  } catch (error) {
    console.error('❌ خطأ في الحصول على السياق من FAISS للـ LLM:', error);
    return '';
  }
}

// إحصائيات FAISS Database
export async function getFaissStats(): Promise<any> {
  try {
    // قراءة معلومات الفهرسة
    try {
      const statusPath = path.join(process.cwd(), 'src/data/faiss-index-status.json');
      const statusData = await fs.readFile(statusPath, 'utf-8');
      const status = JSON.parse(statusData);
      
      // التحقق من وجود ملفات FAISS
      const dbPath = path.join(process.cwd(), 'faiss_database');
      let filesExist = false;
      try {
        const files = await fs.readdir(dbPath);
        filesExist = files.length > 0;
      } catch (error) {
        filesExist = false;
      }
      
      return {
        total_documents: status.total_chunks || 0,
        indexed_at: status.indexed_at,
        embedding_type: status.embedding_type,
        embedding_dimension: status.embedding_dimension,
        database_path: status.database_path,
        file_source: status.file_source,
        file_size: status.file_size,
        files_exist: filesExist,
        status: filesExist ? 'ready' : 'needs_indexing',
        type: 'FAISS_Vector_Database'
      };
    } catch (error) {
      return {
        total_documents: 0,
        status: 'not_indexed',
        type: 'FAISS_Vector_Database',
        error: 'لم يتم العثور على معلومات الفهرسة'
      };
    }
  } catch (error) {
    console.error('❌ خطأ في الحصول على إحصائيات FAISS:', error);
    return { error: 'فشل في الحصول على الإحصائيات' };
  }
}
