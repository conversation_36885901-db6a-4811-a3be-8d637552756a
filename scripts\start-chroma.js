const { spawn } = require('child_process');
const path = require('path');

async function startChromaServer() {
  try {
    console.log('🚀 بدء تشغيل Chroma Server...');
    
    // تشغيل Chroma server باستخدام Python
    const chromaProcess = spawn('python', ['-m', 'chromadb.cli.cli', 'run', '--host', 'localhost', '--port', '8000'], {
      stdio: 'inherit',
      cwd: process.cwd()
    });

    chromaProcess.on('error', (error) => {
      console.error('❌ خطأ في تشغيل Chroma Server:', error.message);
      
      // محاولة تشغيل بطريقة أخرى
      console.log('🔄 محاولة تشغيل Chroma بطريقة أخرى...');
      
      const alternativeProcess = spawn('npx', ['chromadb'], {
        stdio: 'inherit',
        cwd: process.cwd()
      });

      alternativeProcess.on('error', (altError) => {
        console.error('❌ فشل في تشغيل Chroma Server:', altError.message);
        console.log('💡 يرجى تثبيت Chroma Server يدوياً:');
        console.log('   pip install chromadb');
        console.log('   أو');
        console.log('   npm install -g chromadb');
        process.exit(1);
      });
    });

    chromaProcess.on('close', (code) => {
      console.log(`Chroma Server توقف بكود: ${code}`);
    });

    // انتظار قليل للتأكد من بدء الخدمة
    setTimeout(() => {
      console.log('✅ Chroma Server يعمل على http://localhost:8000');
    }, 3000);

  } catch (error) {
    console.error('❌ خطأ في تشغيل Chroma Server:', error);
  }
}

if (require.main === module) {
  startChromaServer();
}

module.exports = { startChromaServer };
