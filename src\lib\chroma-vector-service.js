const { MemoryVectorStore } = require('langchain/vectorstores/memory');
const { RecursiveCharacterTextSplitter } = require('langchain/text_splitter');
const { Document } = require('langchain/document');
const fs = require('fs').promises;
const path = require('path');

// نظام embeddings محلي بسيط متوافق مع LangChain
class LocalEmbeddings {
  // إنشاء embedding بسيط للنص
  async embedDocuments(texts) {
    return texts.map(text => this.createEmbedding(text));
  }

  async embedQuery(text) {
    return this.createEmbedding(text);
  }

  createEmbedding(text) {
    const words = text.toLowerCase().split(/\s+/);
    const embedding = new Array(384).fill(0); // حجم ثابت للـ embedding
    
    // إنشاء embedding بناءً على الكلمات والمصطلحات المهمة
    const importantTerms = [
      'منتج', 'مبيعات', 'عميل', 'فاتورة', 'مخزون', 'كمية', 'سعر', 'مبلغ',
      'تاريخ', 'فرع', 'مورد', 'موزع', 'باركود', 'تصنيف', 'فئة',
      'ItemName', 'ClientName', 'Amount', 'Quantity', 'UnitPrice', 'TheDate',
      'BranchName', 'DocumentName', 'CategoryName', 'DistributorName',
      'tbltemp_ItemsMain', 'tbltemp_Inv_MainInvoice', 'الجدول', 'النية'
    ];

    // تسجيل نقاط للكلمات المهمة
    for (let i = 0; i < words.length; i++) {
      const word = words[i];
      
      // نقاط إضافية للمصطلحات المهمة
      const termIndex = importantTerms.findIndex(term => 
        word.includes(term.toLowerCase()) || term.toLowerCase().includes(word)
      );
      
      if (termIndex !== -1) {
        const index = (termIndex * 13) % embedding.length;
        embedding[index] += 3; // نقاط عالية للمصطلحات المهمة
      }
      
      // تسجيل عام للكلمات
      for (let j = 0; j < word.length; j++) {
        const charCode = word.charCodeAt(j);
        const index = (charCode + i + j) % embedding.length;
        embedding[index] += 1;
      }
    }
    
    // تطبيع الـ vector
    const magnitude = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
    return embedding.map(val => magnitude > 0 ? val / magnitude : 0);
  }
}

// إعداد Vector Store
let vectorStore = null;
let embeddings = null;

// تهيئة نظام Vector Database المحلي
async function initializeChromaVector() {
  try {
    console.log('🚀 بدء تهيئة Vector Database المحلي...');
    
    // إنشاء Local Embeddings
    embeddings = new LocalEmbeddings();
    
    // إنشاء Vector Store (استخدام MemoryVectorStore كبديل محلي)
    vectorStore = new MemoryVectorStore(embeddings);

    // إنشاء مجلد قاعدة البيانات للحفظ المستقبلي
    const dbPath = path.join(process.cwd(), 'chroma_database');
    try {
      await fs.access(dbPath);
    } catch {
      await fs.mkdir(dbPath, { recursive: true });
      console.log('📁 تم إنشاء مجلد قاعدة البيانات:', dbPath);
    }

    console.log('✅ تم تهيئة Vector Database المحلي بنجاح');
  } catch (error) {
    console.error('❌ خطأ في تهيئة Vector Database المحلي:', error);
    throw error;
  }
}

// استخراج metadata من النص
function extractMetadata(text, chunkIndex) {
  const metadata = {
    chunk_index: chunkIndex,
    length: text.length,
    type: 'knowledge_base',
    embedding_type: 'local_vector'
  };

  // استخراج نوع المحتوى
  if (text.includes('الجدول')) {
    metadata.content_type = 'table_description';
    
    // استخراج اسم الجدول
    const tableMatch = text.match(/الجدول.*?:\s*(\w+)/);
    if (tableMatch) {
      metadata.table_name = tableMatch[1];
    }
  } else if (text.includes('النية:') || text.includes('intent:')) {
    metadata.content_type = 'intent';
    
    // استخراج اسم النية
    const intentMatch = text.match(/intent\s*:\s*(\w+)/);
    if (intentMatch) {
      metadata.intent_name = intentMatch[1];
    }
  } else if (text.includes('أوصاف الأعمدة:')) {
    metadata.content_type = 'column_descriptions';
  } else if (text.includes('العلاقات بين الجداول:')) {
    metadata.content_type = 'table_relationships';
  } else if (text.includes('أمثلة تطبيقية:')) {
    metadata.content_type = 'examples';
  } else if (text.includes('الكيانات')) {
    metadata.content_type = 'entities';
  }

  // استخراج الجداول المذكورة
  const tableMatches = text.match(/tbltemp_\w+/g);
  if (tableMatches) {
    metadata.mentioned_tables = [...new Set(tableMatches)];
  }

  // استخراج الأعمدة المذكورة
  const columnMatches = text.match(/\b[A-Z][a-zA-Z]*(?:ID|Name|Date|Amount|Quantity|Price)\b/g);
  if (columnMatches) {
    metadata.mentioned_columns = [...new Set(columnMatches)];
  }

  return metadata;
}

// تحميل وفهرسة محتوى ملف rga.txt
async function indexChromaKnowledgeBase() {
  try {
    if (!vectorStore || !embeddings) {
      throw new Error('Vector Database غير مهيأ');
    }

    console.log('📚 بدء فهرسة قاعدة المعرفة...');

    // قراءة ملف rga.txt
    const rgaPath = path.join(process.cwd(), 'src/data/rga.txt');
    const rgaContent = await fs.readFile(rgaPath, 'utf-8');

    console.log(`📄 تم قراءة الملف: ${rgaContent.length} حرف`);

    // إنشاء Text Splitter
    const textSplitter = new RecursiveCharacterTextSplitter({
      chunkSize: 1000,
      chunkOverlap: 200,
    });

    // تقسيم النص إلى chunks
    const chunks = await textSplitter.splitText(rgaContent);
    console.log(`🔪 تم تقسيم النص إلى ${chunks.length} قطعة`);

    // إنشاء Documents مع metadata
    const documents = chunks.map((chunk, index) => {
      const metadata = extractMetadata(chunk, index);
      return new Document({
        pageContent: chunk,
        metadata
      });
    });

    console.log('💾 إضافة Documents إلى Vector Store...');

    // إضافة Documents إلى Vector Store
    await vectorStore.addDocuments(documents);

    console.log(`✅ تم فهرسة ${chunks.length} قطعة بنجاح`);

    // حفظ معلومات الفهرسة
    const indexInfo = {
      indexed_at: new Date().toISOString(),
      total_chunks: chunks.length,
      embedding_type: 'local_vector',
      database_path: path.join(process.cwd(), 'chroma_database'),
      status: 'ready'
    };

    await fs.writeFile(
      path.join(process.cwd(), 'src/data/chroma-vector-status.json'),
      JSON.stringify(indexInfo, null, 2)
    );

    console.log('💾 تم حفظ معلومات الفهرسة');

  } catch (error) {
    console.error('❌ خطأ في فهرسة قاعدة المعرفة:', error);
    throw error;
  }
}

// البحث في Vector Store
async function searchChromaKnowledge(query, limit = 5) {
  try {
    if (!vectorStore) {
      throw new Error('Vector Database غير مهيأ');
    }

    console.log(`🔍 البحث في Vector Store: "${query}"`);

    // تنفيذ البحث
    const results = await vectorStore.similaritySearchWithScore(query, limit);

    console.log(`📋 تم العثور على ${results.length} نتائج`);

    // تنسيق النتائج
    const formattedResults = results.map((result, index) => ({
      content: result[0].pageContent,
      metadata: result[0].metadata,
      similarity: 1 - result[1], // تحويل المسافة إلى تشابه
      distance: result[1],
      id: `vector_result_${index}`
    }));

    return formattedResults;
  } catch (error) {
    console.error('❌ خطأ في البحث:', error);
    return [];
  }
}

// الحصول على السياق المناسب للنموذج اللغوي
async function getChromaContextForLLM(query) {
  try {
    console.log('🎯 الحصول على السياق للنموذج اللغوي...');

    // البحث في أنواع مختلفة من المحتوى
    const results = await searchChromaKnowledge(query, 5);

    // تكوين السياق المنظم للـ LLM
    const context = results
      .map(result => {
        const type = result.metadata.content_type || 'معلومات عامة';
        return `[${type}]\n${result.content}`;
      })
      .join('\n\n---\n\n');

    console.log(`✅ تم تكوين سياق للـ LLM بطول ${context.length} حرف من ${results.length} مصادر`);

    return context;
  } catch (error) {
    console.error('❌ خطأ في الحصول على السياق للـ LLM:', error);
    return '';
  }
}

// إحصائيات Vector Database
async function getChromaVectorStats() {
  try {
    // قراءة معلومات الفهرسة
    try {
      const statusPath = path.join(process.cwd(), 'src/data/chroma-vector-status.json');
      const statusData = await fs.readFile(statusPath, 'utf-8');
      const status = JSON.parse(statusData);
      
      return {
        total_documents: status.total_chunks || 0,
        indexed_at: status.indexed_at,
        embedding_type: status.embedding_type,
        database_path: status.database_path,
        status: status.status || 'unknown',
        type: 'Local_Vector_Database'
      };
    } catch (error) {
      return {
        total_documents: 0,
        status: 'not_indexed',
        type: 'Local_Vector_Database'
      };
    }
  } catch (error) {
    console.error('❌ خطأ في الحصول على إحصائيات Vector Database:', error);
    return { error: 'فشل في الحصول على الإحصائيات' };
  }
}

// دالة وهمية لإيقاف الخادم (للتوافق)
async function stopChromaServer() {
  console.log('ℹ️ لا يوجد خادم منفصل لإيقافه');
}

module.exports = {
  initializeChromaVector,
  indexChromaKnowledgeBase,
  searchChromaKnowledge,
  getChromaContextForLLM,
  getChromaVectorStats,
  stopChromaServer
};
