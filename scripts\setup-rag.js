const fs = require('fs').promises;
const path = require('path');

async function setupRAGSystem() {
  try {
    console.log('🚀 بدء إعداد نظام RAG...');

    // تحميل المكتبات المطلوبة
    const { MemoryVectorStore } = await import('langchain/vectorstores/memory');
    const { OpenAIEmbeddings } = await import('@langchain/openai');
    const { RecursiveCharacterTextSplitter } = await import('langchain/text_splitter');
    const { Document } = await import('langchain/document');

    console.log('✅ تم تحميل المكتبات بنجاح');

    // إنشاء OpenAI Embeddings
    const embeddings = new OpenAIEmbeddings({
      openAIApiKey: process.env.OPENAI_API_KEY || 'sk-test-key',
      modelName: 'text-embedding-3-small',
    });

    console.log('✅ تم إنشاء Embeddings');

    // إنشاء Vector Store
    const vectorStore = new MemoryVectorStore(embeddings);

    console.log('✅ تم إنشاء Vector Store');

    // قراءة ملف rga.txt
    const rgaPath = path.join(__dirname, '..', 'src', 'data', 'rga.txt');
    const rgaContent = await fs.readFile(rgaPath, 'utf-8');

    console.log(`📄 تم قراءة الملف: ${rgaContent.length} حرف`);

    // إنشاء Text Splitter
    const textSplitter = new RecursiveCharacterTextSplitter({
      chunkSize: 1000,
      chunkOverlap: 200,
    });

    // تقسيم النص إلى chunks
    const chunks = await textSplitter.splitText(rgaContent);
    console.log(`🔪 تم تقسيم النص إلى ${chunks.length} قطعة`);

    // دالة لاستخراج metadata
    function extractMetadata(text, chunkIndex) {
      const metadata = {
        chunk_index: chunkIndex,
        length: text.length,
        type: 'knowledge_base'
      };

      // استخراج نوع المحتوى
      if (text.includes('الجدول')) {
        metadata.content_type = 'table_description';
        
        // استخراج اسم الجدول
        const tableMatch = text.match(/الجدول.*?:\s*(\w+)/);
        if (tableMatch) {
          metadata.table_name = tableMatch[1];
        }
      } else if (text.includes('النية:') || text.includes('intent:')) {
        metadata.content_type = 'intent';
        
        // استخراج اسم النية
        const intentMatch = text.match(/intent\s*:\s*(\w+)/);
        if (intentMatch) {
          metadata.intent_name = intentMatch[1];
        }
      } else if (text.includes('أوصاف الأعمدة:')) {
        metadata.content_type = 'column_descriptions';
      } else if (text.includes('العلاقات بين الجداول:')) {
        metadata.content_type = 'table_relationships';
      } else if (text.includes('أمثلة تطبيقية:')) {
        metadata.content_type = 'examples';
      } else if (text.includes('الكيانات')) {
        metadata.content_type = 'entities';
      }

      // استخراج الجداول المذكورة
      const tableMatches = text.match(/tbltemp_\w+/g);
      if (tableMatches) {
        metadata.mentioned_tables = [...new Set(tableMatches)];
      }

      // استخراج الأعمدة المذكورة
      const columnMatches = text.match(/\b[A-Z][a-zA-Z]*(?:ID|Name|Date|Amount|Quantity|Price)\b/g);
      if (columnMatches) {
        metadata.mentioned_columns = [...new Set(columnMatches)];
      }

      return metadata;
    }

    // إنشاء Documents مع metadata
    const documents = chunks.map((chunk, index) => {
      const metadata = extractMetadata(chunk, index);
      return new Document({
        pageContent: chunk,
        metadata
      });
    });

    console.log('📝 تم إنشاء Documents مع metadata');

    // إضافة Documents إلى Vector Store
    await vectorStore.addDocuments(documents);

    console.log(`✅ تم فهرسة ${chunks.length} قطعة من المعرفة بنجاح`);

    // اختبار البحث
    console.log('🔍 اختبار البحث...');
    const testResults = await vectorStore.similaritySearchWithScore('أكثر المنتجات مبيعاً', 3);

    console.log(`📋 نتائج الاختبار: ${testResults.length} نتائج`);
    if (testResults.length > 0) {
      console.log('📄 أول نتيجة:', testResults[0][0].pageContent.substring(0, 100) + '...');
      console.log('📊 درجة التشابه:', (1 - testResults[0][1]).toFixed(3));
    }

    // حفظ معلومات الإعداد
    const setupInfo = {
      setup_date: new Date().toISOString(),
      total_chunks: chunks.length,
      status: 'ready',
      system_type: 'RAG_with_LangChain'
    };

    await fs.writeFile(
      path.join(__dirname, '..', 'src', 'data', 'vector-index-status.json'),
      JSON.stringify(setupInfo, null, 2)
    );

    console.log('💾 تم حفظ معلومات الإعداد');

    // اختبار أنواع مختلفة من الاستعلامات
    console.log('🧪 اختبار أنواع مختلفة من الاستعلامات...');
    
    const testQueries = [
      'تفاصيل جدول المنتجات',
      'العلاقات بين الجداول',
      'أمثلة استعلامات SQL',
      'النيات والكيانات'
    ];

    for (const query of testQueries) {
      const results = await vectorStore.similaritySearchWithScore(query, 2);
      console.log(`🔍 "${query}": ${results.length} نتائج`);
    }

    console.log('🎉 تم إعداد نظام RAG بنجاح!');
    console.log('📋 الملخص:');
    console.log(`   - إجمالي القطع: ${chunks.length}`);
    console.log(`   - نوع النظام: RAG with LangChain`);
    console.log(`   - حالة الفهرسة: جاهز`);

  } catch (error) {
    console.error('❌ خطأ في إعداد نظام RAG:', error);
    process.exit(1);
  }
}

// تشغيل الإعداد
if (require.main === module) {
  setupRAGSystem();
}

module.exports = { setupRAGSystem };
