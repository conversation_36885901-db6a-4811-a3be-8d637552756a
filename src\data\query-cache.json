{"queries": [{"id": "59e62fc6916171542b9df8309792d410", "query": "المنتج الاكثر مبيعاً ", "sql": "SELECT TOP 5 tbltemp_ItemsMain.ItemName, SUM(tbltemp_ItemsMain.Quantity) AS TotalQuantity, SUM(tbltemp_ItemsMain.Amount) AS TotalRevenue\n            FROM tbltemp_ItemsMain\n            WHERE tbltemp_ItemsMain.DocumentName = 'فاتورة مبيعات'\n            GROUP BY tbltemp_ItemsMain.ItemName\n            ORDER BY TotalQuantity DESC", "data": [{"ItemName": "قرص صلب 1TB", "TotalQuantity": 363, "TotalRevenue": 101640}, {"ItemName": "ميكروويف", "TotalQuantity": 346, "TotalRevenue": 110720}, {"ItemName": "طاولة جانبية", "TotalQuantity": 344, "TotalRevenue": 75680}, {"ItemName": "ت<PERSON><PERSON> خلاص", "TotalQuantity": 330, "TotalRevenue": 14850}, {"ItemName": "مكي<PERSON> هواء", "TotalQuantity": 330, "TotalRevenue": 594000}], "analysis": "تحليل البيانات يشير إلى أن 'قرص صلب1TB' هو المنتج الأكثر مبيعاً، حيث حقق 363 وحدة مبيعات، بقيمة إيرادات 101,640. يليه 'ميكروويف' و'طاولة جانبية' في الترتيب. يُلاحظ أن هناك تفاوت كبير في الإيرادات بين المنتجات، حيث حقق 'مكيف هواء' إيرادات 594,000 رغم أنه مبيعاته مماثلة لمنتجات أخرى.", "timestamp": "2025-07-27T17:13:41.333Z", "expiresAt": "2025-07-27T18:13:41.333Z", "executionTime": 4338, "dataHash": "3c22683de9cac3872cac53719a40d414"}, {"id": "f29e3ccdb3b5d48de5bec65b6eecabe0", "query": "تفصيل بيع المنتج قرص صلب ", "sql": "SELECT TOP 10 tbltemp_ItemsMain.ItemName, SUM(tbltemp_ItemsMain.Quantity) AS TotalQuantity FROM tbltemp_ItemsMain WHERE tbltemp_ItemsMain.ItemName = 'قرص صلب' AND tbltemp_ItemsMain.DocumentName = 'فاتورة مبيعات' GROUP BY tbltemp_ItemsMain.ItemName ORDER BY TotalQuantity DESC", "data": [], "analysis": "تحليل الاستعلام يشير إلى رغبة في تفصيل بيع المنتج 'قرص صلب'، ولكن لا توجد بيانات متاحة للتحليل. النظام لم يستطع العثور على أي سجلات ذات صلة.", "timestamp": "2025-07-27T17:14:02.242Z", "expiresAt": "2025-07-27T18:14:02.242Z", "executionTime": 3342, "dataHash": "d751713988987e9331980363e24189ce"}], "version": "1.0.0", "lastCleanup": "2025-07-27T17:13:36.995Z"}