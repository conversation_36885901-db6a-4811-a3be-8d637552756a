import { MemoryVectorStore } from 'langchain/vectorstores/memory';
import { OpenAIEmbeddings } from '@langchain/openai';
import { RecursiveCharacterTextSplitter } from 'langchain/text_splitter';
import { Document } from 'langchain/document';
import fs from 'fs/promises';
import path from 'path';

// إعداد Vector Store
let vectorStore: MemoryVectorStore | null = null;
let embeddings: OpenAIEmbeddings | null = null;

// تهيئة Vector Database
export async function initializeVectorDB(): Promise<void> {
  try {
    console.log('🚀 بدء تهيئة Vector Database...');

    // إنشاء OpenAI Embeddings
    embeddings = new OpenAIEmbeddings({
      openAIApiKey: process.env.OPENAI_API_KEY || 'sk-test-key',
      modelName: 'text-embedding-3-small',
    });

    // إنشاء Vector Store فارغ
    vectorStore = new MemoryVectorStore(embeddings);

    console.log('✅ تم تهيئة Vector Database بنجاح');
  } catch (error) {
    console.error('❌ خطأ في تهيئة Vector Database:', error);
    throw error;
  }
}

// تحويل النص إلى chunks صغيرة
function splitTextIntoChunks(text: string, maxChunkSize: number = 1000): string[] {
  const chunks: string[] = [];
  const lines = text.split('\n');
  let currentChunk = '';

  for (const line of lines) {
    if (currentChunk.length + line.length > maxChunkSize && currentChunk.length > 0) {
      chunks.push(currentChunk.trim());
      currentChunk = line;
    } else {
      currentChunk += (currentChunk ? '\n' : '') + line;
    }
  }

  if (currentChunk.trim()) {
    chunks.push(currentChunk.trim());
  }

  return chunks;
}

// استخراج metadata من النص
function extractMetadata(text: string, chunkIndex: number): Record<string, any> {
  const metadata: Record<string, any> = {
    chunk_index: chunkIndex,
    length: text.length,
    type: 'knowledge_base'
  };

  // استخراج نوع المحتوى
  if (text.includes('الجدول')) {
    metadata.content_type = 'table_description';
    
    // استخراج اسم الجدول
    const tableMatch = text.match(/الجدول.*?:\s*(\w+)/);
    if (tableMatch) {
      metadata.table_name = tableMatch[1];
    }
  } else if (text.includes('النية:') || text.includes('intent:')) {
    metadata.content_type = 'intent';
    
    // استخراج اسم النية
    const intentMatch = text.match(/intent\s*:\s*(\w+)/);
    if (intentMatch) {
      metadata.intent_name = intentMatch[1];
    }
  } else if (text.includes('أوصاف الأعمدة:')) {
    metadata.content_type = 'column_descriptions';
  } else if (text.includes('العلاقات بين الجداول:')) {
    metadata.content_type = 'table_relationships';
  } else if (text.includes('أمثلة تطبيقية:')) {
    metadata.content_type = 'examples';
  } else if (text.includes('الكيانات')) {
    metadata.content_type = 'entities';
  }

  // استخراج الجداول المذكورة
  const tableMatches = text.match(/tbltemp_\w+/g);
  if (tableMatches) {
    metadata.mentioned_tables = [...new Set(tableMatches)];
  }

  // استخراج الأعمدة المذكورة
  const columnMatches = text.match(/\b[A-Z][a-zA-Z]*(?:ID|Name|Date|Amount|Quantity|Price)\b/g);
  if (columnMatches) {
    metadata.mentioned_columns = [...new Set(columnMatches)];
  }

  return metadata;
}

// تحميل وفهرسة محتوى ملف rga.txt
export async function indexKnowledgeBase(): Promise<void> {
  try {
    if (!vectorStore || !embeddings) {
      throw new Error('Vector Database غير مهيأ');
    }

    console.log('📚 بدء فهرسة قاعدة المعرفة...');

    // قراءة ملف rga.txt
    const rgaPath = path.join(process.cwd(), 'src/data/rga.txt');
    const rgaContent = await fs.readFile(rgaPath, 'utf-8');

    console.log(`📄 تم قراءة الملف: ${rgaContent.length} حرف`);

    // إنشاء Text Splitter
    const textSplitter = new RecursiveCharacterTextSplitter({
      chunkSize: 1000,
      chunkOverlap: 200,
    });

    // تقسيم النص إلى chunks
    const chunks = await textSplitter.splitText(rgaContent);
    console.log(`🔪 تم تقسيم النص إلى ${chunks.length} قطعة`);

    // إنشاء Documents مع metadata
    const documents = chunks.map((chunk, index) => {
      const metadata = extractMetadata(chunk, index);
      return new Document({
        pageContent: chunk,
        metadata
      });
    });

    // إضافة Documents إلى Vector Store
    await vectorStore.addDocuments(documents);

    console.log(`✅ تم فهرسة ${chunks.length} قطعة من المعرفة بنجاح`);

    // حفظ معلومات الفهرسة
    const indexInfo = {
      indexed_at: new Date().toISOString(),
      total_chunks: chunks.length,
      status: 'ready'
    };

    await fs.writeFile(
      path.join(process.cwd(), 'src/data/vector-index-status.json'),
      JSON.stringify(indexInfo, null, 2)
    );

    console.log('💾 تم حفظ معلومات الفهرسة');

  } catch (error) {
    console.error('❌ خطأ في فهرسة قاعدة المعرفة:', error);
    throw error;
  }
}

// البحث في قاعدة المعرفة
export async function searchKnowledgeBase(
  query: string,
  limit: number = 5,
  filter?: Record<string, any>
): Promise<any[]> {
  try {
    if (!vectorStore) {
      throw new Error('Vector Database غير مهيأ');
    }

    console.log(`🔍 البحث عن: "${query}"`);

    // تنفيذ البحث
    const results = await vectorStore.similaritySearchWithScore(query, limit);

    console.log(`📋 تم العثور على ${results.length} نتائج`);

    // تنسيق النتائج
    const formattedResults = results.map((result, index) => ({
      content: result[0].pageContent,
      metadata: result[0].metadata,
      similarity: 1 - result[1], // تحويل المسافة إلى تشابه
      id: `result_${index}`
    }));

    // تطبيق الفلتر إذا كان موجوداً
    if (filter) {
      return formattedResults.filter(result => {
        return Object.entries(filter).every(([key, value]) =>
          result.metadata[key] === value
        );
      });
    }

    return formattedResults;
  } catch (error) {
    console.error('❌ خطأ في البحث:', error);
    return [];
  }
}

// البحث المتقدم بناءً على نوع المحتوى
export async function searchByContentType(
  query: string,
  contentType: string,
  limit: number = 3
): Promise<any[]> {
  return searchKnowledgeBase(query, limit, { content_type: contentType });
}

// الحصول على السياق المناسب للاستعلام
export async function getRelevantContext(query: string): Promise<string> {
  try {
    console.log('🎯 الحصول على السياق المناسب للاستعلام...');

    // البحث في أنواع مختلفة من المحتوى
    const [
      tableResults,
      intentResults,
      exampleResults,
      generalResults
    ] = await Promise.all([
      searchByContentType(query, 'table_description', 2),
      searchByContentType(query, 'intent', 2),
      searchByContentType(query, 'examples', 2),
      searchKnowledgeBase(query, 3)
    ]);

    // دمج النتائج وإزالة التكرار
    const allResults = [...tableResults, ...intentResults, ...exampleResults, ...generalResults];
    const uniqueResults = allResults.filter((result, index, self) => 
      index === self.findIndex(r => r.id === result.id)
    );

    // ترتيب حسب التشابه
    uniqueResults.sort((a, b) => b.similarity - a.similarity);

    // أخذ أفضل النتائج
    const topResults = uniqueResults.slice(0, 5);

    // تكوين السياق
    const context = topResults
      .map(result => result.content)
      .join('\n\n---\n\n');

    console.log(`✅ تم تكوين سياق بطول ${context.length} حرف من ${topResults.length} مصادر`);

    return context;
  } catch (error) {
    console.error('❌ خطأ في الحصول على السياق:', error);
    return '';
  }
}

// إحصائيات قاعدة البيانات
export async function getDatabaseStats(): Promise<any> {
  try {
    if (!vectorStore) {
      return { error: 'Vector Database غير مهيأ' };
    }

    // قراءة معلومات الفهرسة
    try {
      const statusPath = path.join(process.cwd(), 'src/data/vector-index-status.json');
      const statusData = await fs.readFile(statusPath, 'utf-8');
      const status = JSON.parse(statusData);

      return {
        total_documents: status.total_chunks || 0,
        indexed_at: status.indexed_at,
        status: status.status || 'unknown'
      };
    } catch (error) {
      return {
        total_documents: 0,
        status: 'not_indexed'
      };
    }
  } catch (error) {
    console.error('❌ خطأ في الحصول على الإحصائيات:', error);
    return { error: 'فشل في الحصول على الإحصائيات' };
  }
}
