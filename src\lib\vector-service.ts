import { ChromaClient, Collection } from 'chromadb';
import fs from 'fs/promises';
import path from 'path';

// إعداد Chroma Client
let chromaClient: ChromaClient | null = null;
let collection: Collection | null = null;

// تهيئة Chroma Database
export async function initializeVectorDB(): Promise<void> {
  try {
    console.log('🚀 بدء تهيئة Vector Database...');

    // إنشاء Chroma client (in-memory)
    const { ChromaClient } = await import('chromadb');
    chromaClient = new ChromaClient();

    // إنشاء أو الحصول على collection
    try {
      collection = await chromaClient.getCollection({
        name: 'sql_knowledge_base'
      });
      console.log('✅ تم العثور على collection موجودة');
    } catch (error) {
      console.log('📝 إنشاء collection جديدة...');
      collection = await chromaClient.createCollection({
        name: 'sql_knowledge_base',
        metadata: {
          description: 'قاعدة معرفة SQL للنظام التجاري',
          created_at: new Date().toISOString(),
          version: '1.0'
        }
      });
      console.log('✅ تم إنشاء collection جديدة');
    }

    console.log('✅ تم تهيئة Vector Database بنجاح');
  } catch (error) {
    console.error('❌ خطأ في تهيئة Vector Database:', error);
    throw error;
  }
}

// تحويل النص إلى chunks صغيرة
function splitTextIntoChunks(text: string, maxChunkSize: number = 1000): string[] {
  const chunks: string[] = [];
  const lines = text.split('\n');
  let currentChunk = '';

  for (const line of lines) {
    if (currentChunk.length + line.length > maxChunkSize && currentChunk.length > 0) {
      chunks.push(currentChunk.trim());
      currentChunk = line;
    } else {
      currentChunk += (currentChunk ? '\n' : '') + line;
    }
  }

  if (currentChunk.trim()) {
    chunks.push(currentChunk.trim());
  }

  return chunks;
}

// استخراج metadata من النص
function extractMetadata(text: string, chunkIndex: number): Record<string, any> {
  const metadata: Record<string, any> = {
    chunk_index: chunkIndex,
    length: text.length,
    type: 'knowledge_base'
  };

  // استخراج نوع المحتوى
  if (text.includes('الجدول')) {
    metadata.content_type = 'table_description';
    
    // استخراج اسم الجدول
    const tableMatch = text.match(/الجدول.*?:\s*(\w+)/);
    if (tableMatch) {
      metadata.table_name = tableMatch[1];
    }
  } else if (text.includes('النية:') || text.includes('intent:')) {
    metadata.content_type = 'intent';
    
    // استخراج اسم النية
    const intentMatch = text.match(/intent\s*:\s*(\w+)/);
    if (intentMatch) {
      metadata.intent_name = intentMatch[1];
    }
  } else if (text.includes('أوصاف الأعمدة:')) {
    metadata.content_type = 'column_descriptions';
  } else if (text.includes('العلاقات بين الجداول:')) {
    metadata.content_type = 'table_relationships';
  } else if (text.includes('أمثلة تطبيقية:')) {
    metadata.content_type = 'examples';
  } else if (text.includes('الكيانات')) {
    metadata.content_type = 'entities';
  }

  // استخراج الجداول المذكورة
  const tableMatches = text.match(/tbltemp_\w+/g);
  if (tableMatches) {
    metadata.mentioned_tables = [...new Set(tableMatches)];
  }

  // استخراج الأعمدة المذكورة
  const columnMatches = text.match(/\b[A-Z][a-zA-Z]*(?:ID|Name|Date|Amount|Quantity|Price)\b/g);
  if (columnMatches) {
    metadata.mentioned_columns = [...new Set(columnMatches)];
  }

  return metadata;
}

// تحميل وفهرسة محتوى ملف rga.txt
export async function indexKnowledgeBase(): Promise<void> {
  try {
    if (!collection) {
      throw new Error('Vector Database غير مهيأ');
    }

    console.log('📚 بدء فهرسة قاعدة المعرفة...');

    // قراءة ملف rga.txt
    const rgaPath = path.join(process.cwd(), 'src/data/rga.txt');
    const rgaContent = await fs.readFile(rgaPath, 'utf-8');

    console.log(`📄 تم قراءة الملف: ${rgaContent.length} حرف`);

    // تقسيم النص إلى chunks
    const chunks = splitTextIntoChunks(rgaContent, 800);
    console.log(`🔪 تم تقسيم النص إلى ${chunks.length} قطعة`);

    // إعداد البيانات للفهرسة
    const documents: string[] = [];
    const metadatas: Record<string, any>[] = [];
    const ids: string[] = [];

    chunks.forEach((chunk, index) => {
      documents.push(chunk);
      metadatas.push(extractMetadata(chunk, index));
      ids.push(`chunk_${index}`);
    });

    // حذف البيانات الموجودة إن وجدت
    try {
      await collection.delete();
      console.log('🗑️ تم حذف البيانات القديمة');
    } catch (error) {
      console.log('ℹ️ لا توجد بيانات قديمة للحذف');
    }

    // إضافة البيانات الجديدة
    await collection.add({
      documents,
      metadatas,
      ids
    });

    console.log(`✅ تم فهرسة ${chunks.length} قطعة من المعرفة بنجاح`);

    // عرض إحصائيات
    const count = await collection.count();
    console.log(`📊 إجمالي العناصر في قاعدة البيانات: ${count}`);

  } catch (error) {
    console.error('❌ خطأ في فهرسة قاعدة المعرفة:', error);
    throw error;
  }
}

// البحث في قاعدة المعرفة
export async function searchKnowledgeBase(
  query: string, 
  limit: number = 5,
  filter?: Record<string, any>
): Promise<any[]> {
  try {
    if (!collection) {
      throw new Error('Vector Database غير مهيأ');
    }

    console.log(`🔍 البحث عن: "${query}"`);

    // تنفيذ البحث
    const results = await collection.query({
      queryTexts: [query],
      nResults: limit,
      where: filter
    });

    console.log(`📋 تم العثور على ${results.documents[0]?.length || 0} نتائج`);

    // تنسيق النتائج
    const formattedResults = [];
    if (results.documents[0] && results.metadatas[0] && results.distances[0]) {
      for (let i = 0; i < results.documents[0].length; i++) {
        formattedResults.push({
          content: results.documents[0][i],
          metadata: results.metadatas[0][i],
          similarity: 1 - (results.distances[0][i] || 0), // تحويل المسافة إلى تشابه
          id: results.ids[0]?.[i]
        });
      }
    }

    return formattedResults;
  } catch (error) {
    console.error('❌ خطأ في البحث:', error);
    return [];
  }
}

// البحث المتقدم بناءً على نوع المحتوى
export async function searchByContentType(
  query: string,
  contentType: string,
  limit: number = 3
): Promise<any[]> {
  return searchKnowledgeBase(query, limit, { content_type: contentType });
}

// الحصول على السياق المناسب للاستعلام
export async function getRelevantContext(query: string): Promise<string> {
  try {
    console.log('🎯 الحصول على السياق المناسب للاستعلام...');

    // البحث في أنواع مختلفة من المحتوى
    const [
      tableResults,
      intentResults,
      exampleResults,
      generalResults
    ] = await Promise.all([
      searchByContentType(query, 'table_description', 2),
      searchByContentType(query, 'intent', 2),
      searchByContentType(query, 'examples', 2),
      searchKnowledgeBase(query, 3)
    ]);

    // دمج النتائج وإزالة التكرار
    const allResults = [...tableResults, ...intentResults, ...exampleResults, ...generalResults];
    const uniqueResults = allResults.filter((result, index, self) => 
      index === self.findIndex(r => r.id === result.id)
    );

    // ترتيب حسب التشابه
    uniqueResults.sort((a, b) => b.similarity - a.similarity);

    // أخذ أفضل النتائج
    const topResults = uniqueResults.slice(0, 5);

    // تكوين السياق
    const context = topResults
      .map(result => result.content)
      .join('\n\n---\n\n');

    console.log(`✅ تم تكوين سياق بطول ${context.length} حرف من ${topResults.length} مصادر`);

    return context;
  } catch (error) {
    console.error('❌ خطأ في الحصول على السياق:', error);
    return '';
  }
}

// إحصائيات قاعدة البيانات
export async function getDatabaseStats(): Promise<any> {
  try {
    if (!collection) {
      return { error: 'Vector Database غير مهيأ' };
    }

    const count = await collection.count();
    
    return {
      total_documents: count,
      collection_name: 'sql_knowledge_base',
      status: 'active'
    };
  } catch (error) {
    console.error('❌ خطأ في الحصول على الإحصائيات:', error);
    return { error: 'فشل في الحصول على الإحصائيات' };
  }
}
