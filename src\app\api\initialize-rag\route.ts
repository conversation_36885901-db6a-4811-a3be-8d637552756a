import { NextRequest, NextResponse } from 'next/server';
import { initializeRAGSystem } from '@/lib/ai-service';

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 بدء تهيئة نظام RAG...');
    
    const success = await initializeRAGSystem();
    
    if (success) {
      console.log('✅ تم تهيئة نظام RAG بنجاح');
      return NextResponse.json({ 
        success: true, 
        message: 'تم تهيئة نظام RAG بنجاح' 
      });
    } else {
      console.log('❌ فشل في تهيئة نظام RAG');
      return NextResponse.json(
        { 
          success: false, 
          error: 'فشل في تهيئة نظام RAG' 
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('❌ خطأ في تهيئة نظام RAG:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: 'خطأ في تهيئة نظام RAG',
        details: error instanceof Error ? error.message : 'خطأ غير معروف'
      },
      { status: 500 }
    );
  }
}
