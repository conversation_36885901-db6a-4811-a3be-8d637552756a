'use client';

import { useState, useEffect } from 'react';
import { Database, BarChart3, <PERSON><PERSON><PERSON>, Bot, Search } from 'lucide-react';
import ChatBotInterface from './ChatBotInterface';
import ResultsDisplay from './ResultsDisplay';
import CacheManager from './CacheManager';
import SettingsPanel from './SettingsPanel';
import PerformanceMonitor from './PerformanceMonitor';
import RAGStatus from './RAGStatus';
import { useQuickNotifications } from './NotificationSystem';

interface MainDashboardProps {
  connectionData: any;
}

interface QueryResult {
  id: string;
  query: string;
  sql: string;
  data: any[];
  analysis: string;
  timestamp: Date;
  visualization?: string;
  success?: boolean;
  cached?: boolean;
}



export default function MainDashboard({ connectionData }: MainDashboardProps) {
  const [currentQuery, setCurrentQuery] = useState('');
  const [queryHistory, setQueryHistory] = useState<QueryResult[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [activeTab, setActiveTab] = useState<'chat' | 'history' | 'cache' | 'rag'>('chat');
  const [chatMessages, setChatMessages] = useState<any[]>([]);
  const [showSettings, setShowSettings] = useState(false);
  const [showPerformance, setShowPerformance] = useState(false);

  const notifications = useQuickNotifications();

  // حفظ واستعادة المحادثات من localStorage
  useEffect(() => {
    const savedMessages = localStorage.getItem('chatMessages');
    if (savedMessages) {
      try {
        setChatMessages(JSON.parse(savedMessages));
      } catch (error) {
        console.error('خطأ في استعادة المحادثات:', error);
      }
    }
  }, []);

  useEffect(() => {
    if (chatMessages.length > 0) {
      localStorage.setItem('chatMessages', JSON.stringify(chatMessages));
    }
  }, [chatMessages]);

  const handleQuerySubmit = async (query: string) => {
    if (!query.trim() || isProcessing) return;

    setIsProcessing(true);
    setCurrentQuery(query);

    try {
      // إرسال الاستعلام للمعالجة
      const response = await fetch('/api/query/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query,
          connectionData
        }),
      });

      const result = await response.json();

      if (response.ok) {
        const newResult: QueryResult = {
          id: Date.now().toString(),
          query,
          sql: result.sql,
          data: result.data,
          analysis: result.analysis,
          timestamp: new Date(),
          visualization: result.visualization
        };

        setQueryHistory(prev => [newResult, ...prev]);
        // البقاء في الواجهة الرئيسية

        // إشعار نجاح
        notifications.success(
          'تم تنفيذ الاستعلام بنجاح',
          `تم العثور على ${result.data.length} صف من البيانات${result.cached ? ' (من التخزين المؤقت)' : ''}`
        );
      } else {
        notifications.error('خطأ في تنفيذ الاستعلام', result.error);
      }
    } catch (error) {
      console.error('خطأ في الشبكة:', error);
      notifications.error('فشل في معالجة الاستعلام', 'تحقق من الاتصال بالإنترنت وحاول مرة أخرى');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-lg dark:bg-gray-800/80 shadow-lg border-b border-gray-200/50 dark:border-gray-700/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center mr-3">
                <Bot className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                  وكيل الذكاء الاصطناعي
                </h1>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  متصل بـ: {connectionData.database}@{connectionData.server}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <button
                onClick={() => setShowSettings(true)}
                className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                title="الإعدادات"
              >
                <Settings className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation Tabs */}
      <div className="bg-white/80 backdrop-blur-lg dark:bg-gray-800/80 border-b border-gray-200/50 dark:border-gray-700/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-8">
            <button
              onClick={() => setActiveTab('chat')}
              className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === 'chat'
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <Bot className="w-4 h-4 inline mr-2" />
              الدردشة الذكية
              {chatMessages.length > 0 && (
                <span className="mr-2 bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-green-900 dark:text-green-300">
                  {chatMessages.length}
                </span>
              )}
            </button>

            <button
              onClick={() => setActiveTab('history')}
              className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === 'history'
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <BarChart3 className="w-4 h-4 inline mr-2" />
              سجل النتائج
              {queryHistory.length > 0 && (
                <span className="mr-2 bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-blue-900 dark:text-blue-300">
                  {queryHistory.length}
                </span>
              )}
            </button>



            <button
              onClick={() => setActiveTab('cache')}
              className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === 'cache'
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <Database className="w-4 h-4 inline mr-2" />
              التخزين المؤقت
            </button>

            <button
              onClick={() => setActiveTab('rag')}
              className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === 'rag'
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <Search className="w-4 h-4 inline mr-2" />
              نظام RAG
            </button>
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <main className={`${activeTab === 'chat' ? '' : 'max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8'}`}>
        {activeTab === 'chat' && (
          <ChatBotInterface
            connectionData={connectionData}
            messages={chatMessages}
            onMessagesChange={setChatMessages}
            onQueryResult={(result) => {
              setQueryHistory(prev => [result, ...prev]);
              notifications.success(
                'تم تنفيذ الاستعلام بنجاح',
                `تم العثور على ${result.data.length} صف من البيانات`
              );
            }}
          />
        )}

        {activeTab === 'history' && (
          <ResultsDisplay
            queryHistory={queryHistory}
            onQuerySelect={(result) => {
              setCurrentQuery(result.query);
              setActiveTab('chat');
            }}
          />
        )}

        {activeTab === 'cache' && (
          <CacheManager />
        )}

        {activeTab === 'rag' && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">نظام RAG</h2>
              <p className="text-gray-600 mb-6">
                نظام Retrieval Augmented Generation يوفر سياقاً ذكياً للاستعلامات من قاعدة المعرفة المتخصصة.
              </p>
            </div>
            <RAGStatus />
          </div>
        )}
      </main>

      {/* Settings Panel */}
      <SettingsPanel
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
      />

      {/* Performance Monitor */}
      <PerformanceMonitor
        isVisible={showPerformance}
        onToggle={() => setShowPerformance(!showPerformance)}
      />
    </div>
  );
}
