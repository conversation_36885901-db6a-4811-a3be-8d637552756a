'use client';

import { useState, useEffect } from 'react';
import { Database, CheckCircle, AlertCircle, RefreshCw, Search, BarChart3 } from 'lucide-react';

interface RAGStats {
  total_documents?: number;
  is_initialized?: boolean;
  system_type?: string;
  status?: string;
  error?: string;
}

export default function RAGStatus() {
  const [ragStats, setRagStats] = useState<RAGStats | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // جلب إحصائيات RAG
  const fetchRAGStats = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/rag-stats');
      if (response.ok) {
        const stats = await response.json();
        setRagStats(stats);
        setLastUpdated(new Date());
      } else {
        setRagStats({ error: 'فشل في جلب الإحصائيات' });
      }
    } catch (error) {
      console.error('خطأ في جلب إحصائيات RAG:', error);
      setRagStats({ error: 'خطأ في الاتصال' });
    } finally {
      setIsLoading(false);
    }
  };

  // تهيئة نظام RAG
  const initializeRAG = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/initialize-rag', { method: 'POST' });
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          await fetchRAGStats();
        } else {
          setRagStats({ error: 'فشل في تهيئة النظام' });
        }
      }
    } catch (error) {
      console.error('خطأ في تهيئة RAG:', error);
      setRagStats({ error: 'خطأ في التهيئة' });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchRAGStats();
  }, []);

  const getStatusColor = () => {
    if (ragStats?.error) return 'text-red-500';
    if (ragStats?.status === 'ready') return 'text-green-500';
    if (ragStats?.is_initialized) return 'text-blue-500';
    return 'text-yellow-500';
  };

  const getStatusIcon = () => {
    if (ragStats?.error) return <AlertCircle className="w-5 h-5" />;
    if (ragStats?.status === 'ready') return <CheckCircle className="w-5 h-5" />;
    if (ragStats?.is_initialized) return <Database className="w-5 h-5" />;
    return <AlertCircle className="w-5 h-5" />;
  };

  const getStatusText = () => {
    if (ragStats?.error) return ragStats.error;
    if (ragStats?.status === 'ready') return 'جاهز للاستخدام';
    if (ragStats?.is_initialized) return 'مهيأ';
    return 'غير مهيأ';
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <div className="p-2 bg-blue-100 rounded-lg">
            <Search className="w-6 h-6 text-blue-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">نظام RAG</h3>
            <p className="text-sm text-gray-600">Retrieval Augmented Generation</p>
          </div>
        </div>
        
        <button
          onClick={fetchRAGStats}
          disabled={isLoading}
          className="p-2 text-gray-500 hover:text-gray-700 disabled:opacity-50"
          title="تحديث الإحصائيات"
        >
          <RefreshCw className={`w-5 h-5 ${isLoading ? 'animate-spin' : ''}`} />
        </button>
      </div>

      {/* حالة النظام */}
      <div className="flex items-center space-x-2 rtl:space-x-reverse mb-4">
        <div className={getStatusColor()}>
          {getStatusIcon()}
        </div>
        <span className={`font-medium ${getStatusColor()}`}>
          {getStatusText()}
        </span>
      </div>

      {/* الإحصائيات */}
      {ragStats && !ragStats.error && (
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="bg-gray-50 rounded-lg p-3">
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <BarChart3 className="w-4 h-4 text-gray-600" />
              <span className="text-sm text-gray-600">المستندات</span>
            </div>
            <p className="text-lg font-semibold text-gray-900 mt-1">
              {ragStats.total_documents || 0}
            </p>
          </div>

          <div className="bg-gray-50 rounded-lg p-3">
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <Database className="w-4 h-4 text-gray-600" />
              <span className="text-sm text-gray-600">النوع</span>
            </div>
            <p className="text-lg font-semibold text-gray-900 mt-1">
              {ragStats.system_type || 'غير محدد'}
            </p>
          </div>
        </div>
      )}

      {/* معلومات إضافية */}
      <div className="text-xs text-gray-500 space-y-1">
        {lastUpdated && (
          <p>آخر تحديث: {lastUpdated.toLocaleTimeString('ar-SA')}</p>
        )}
        <p>نظام RAG يوفر سياقاً ذكياً للاستعلامات من قاعدة المعرفة</p>
      </div>

      {/* أزرار الإجراءات */}
      <div className="mt-4 flex space-x-2 rtl:space-x-reverse">
        {(!ragStats?.is_initialized || ragStats?.error) && (
          <button
            onClick={initializeRAG}
            disabled={isLoading}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 text-sm"
          >
            {isLoading ? 'جاري التهيئة...' : 'تهيئة النظام'}
          </button>
        )}
        
        <button
          onClick={fetchRAGStats}
          disabled={isLoading}
          className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 disabled:opacity-50 text-sm"
        >
          تحديث
        </button>
      </div>

      {/* مؤشر التحميل */}
      {isLoading && (
        <div className="mt-4 flex items-center space-x-2 rtl:space-x-reverse text-blue-600">
          <RefreshCw className="w-4 h-4 animate-spin" />
          <span className="text-sm">جاري المعالجة...</span>
        </div>
      )}
    </div>
  );
}
