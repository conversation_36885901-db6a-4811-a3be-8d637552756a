import Groq from 'groq-sdk';
import fs from 'fs/promises';
import path from 'path';

// Helper function to extract and parse JSON from AI responses
function extractAndParseJSON(content: string): any {
  console.log('🔍 محاولة تحليل الاستجابة:', content.substring(0, 200) + '...');

  try {
    // First try to parse directly
    const result = JSON.parse(content);
    console.log('✅ تم تحليل JSON مباشرة');
    return result;
  } catch (directError) {
    console.log('❌ فشل التحليل المباشر:', directError instanceof Error ? directError.message : String(directError));

    // Try to extract JSO<PERSON> from markdown code blocks
    const jsonBlockMatch = content.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/);
    if (jsonBlockMatch) {
      try {
        const result = JSON.parse(jsonBlockMatch[1]);
        console.log('✅ تم استخراج JSON من markdown code block');
        return result;
      } catch (markdownError) {
        console.log('❌ فشل تحليل JSON من markdown:', markdownError instanceof Error ? markdownError.message : String(markdownError));
      }
    }

    // Try to find JSON object in the text (look for complete JSON objects)
    const jsonObjectMatch = content.match(/\{[\s\S]*?"success"\s*:\s*true[\s\S]*?\}/);
    if (jsonObjectMatch) {
      try {
        const result = JSON.parse(jsonObjectMatch[0]);
        console.log('✅ تم استخراج JSON object من النص');
        return result;
      } catch (objectError) {
        console.log('❌ فشل تحليل JSON object:', objectError instanceof Error ? objectError.message : String(objectError));
      }
    }

    // Try to find any JSON-like content between braces
    const jsonStart = content.indexOf('{');
    const jsonEnd = content.lastIndexOf('}');
    if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
      try {
        const jsonContent = content.substring(jsonStart, jsonEnd + 1);
        const result = JSON.parse(jsonContent);
        console.log('✅ تم استخراج JSON من النص');
        return result;
      } catch (extractError) {
        console.log('❌ فشل استخراج JSON من النص:', extractError instanceof Error ? extractError.message : String(extractError));
      }
    }

    // Look for JSON in the last part of the content (sometimes AI puts JSON at the end)
    const lines = content.split('\n');
    for (let i = lines.length - 1; i >= 0; i--) {
      const line = lines[i].trim();
      if (line.startsWith('{') && line.endsWith('}')) {
        try {
          const result = JSON.parse(line);
          console.log('✅ تم استخراج JSON من آخر سطر');
          return result;
        } catch (lineError) {
          // Continue to next line
        }
      }
    }

    console.error('❌ فشل في جميع محاولات تحليل JSON. المحتوى:', content);
    throw new Error(`Could not extract valid JSON from response: ${content.substring(0, 200)}...`);
  }
}

// إعداد Groq API
const groq = new Groq({
  apiKey: process.env.GROQ_API_KEY || '********************************************************',
});

const MODEL_NAME = 'meta-llama/llama-4-scout-17b-16e-instruct';

// Cache بسيط للاستعلامات المتشابهة
const queryCache = new Map<string, any>();

// دالة fallback للاستعلامات الشائعة
function generateCommonQuery(queryAnalysis: any): any {
  const intent = queryAnalysis.intent?.toLowerCase() || '';
  const entities = queryAnalysis.entities || {};

  // أكثر المنتجات مبيعاً
  if (intent.includes('منتج') && (intent.includes('مبيع') || intent.includes('أكثر'))) {
    // تحديد العدد المطلوب بدقة
    let topN = 5; // افتراضي
    if (entities.top_n) {
      topN = parseInt(entities.top_n.toString()) || 5;
    } else if (queryAnalysis.intent.includes('منتجين') || queryAnalysis.intent.includes('اثنين')) {
      topN = 2;
    } else if (queryAnalysis.intent.includes('ثلاثة')) {
      topN = 3;
    }
    console.log(`🔢 استخدام العدد المحدد: ${topN} للاستعلام عن المنتجات`);

    return {
      success: true,
      sql: `SELECT TOP ${topN} tbltemp_ItemsMain.ItemName, SUM(tbltemp_ItemsMain.Quantity) AS TotalQuantity, SUM(tbltemp_ItemsMain.Amount) AS TotalRevenue
            FROM tbltemp_ItemsMain
            WHERE tbltemp_ItemsMain.DocumentName = 'فاتورة مبيعات'
            GROUP BY tbltemp_ItemsMain.ItemName
            ORDER BY TotalQuantity DESC`,
      explanation: `استعلام لعرض أكثر ${topN} منتجات مبيعاً من حيث الكمية، مع عرض إجمالي الإيرادات لكل منتج`,
      performance_tips: ['استخدم فهرس على DocumentName', 'استخدم فهرس على ItemName'],
      alternative_queries: [`استعلام مرتب حسب الإيرادات: ORDER BY TotalRevenue DESC`]
    };
  }

  // تفاصيل منتج محدد
  if (intent.includes('تفاصيل') && entities.product_name) {
    const productName = entities.product_name;
    const documentType = intent.includes('مشتريات') ? 'فاتورة مشتريات' : 'فاتورة مبيعات';

    return {
      success: true,
      sql: `SELECT tbltemp_ItemsMain.ItemName, tbltemp_ItemsMain.Quantity, tbltemp_ItemsMain.Amount, tbltemp_ItemsMain.UnitPrice, tbltemp_ItemsMain.TheDate, tbltemp_ItemsMain.DocumentName
            FROM tbltemp_ItemsMain
            WHERE tbltemp_ItemsMain.ItemName LIKE '%${productName}%' AND tbltemp_ItemsMain.DocumentName = '${documentType}'
            ORDER BY tbltemp_ItemsMain.TheDate DESC`,
      explanation: `استعلام لعرض تفاصيل ${documentType === 'فاتورة مشتريات' ? 'مشتريات' : 'مبيعات'} المنتج: ${productName}`,
      performance_tips: ['استخدم فهرس على ItemName', 'استخدم فهرس على DocumentName'],
      alternative_queries: [`استعلام مجمع: GROUP BY ItemName`]
    };
  }

  // أكثر العملاء شراءً
  if (intent.includes('عميل') && (intent.includes('شراء') || intent.includes('أكثر'))) {
    const topN = entities.top_n || 5;

    return {
      success: true,
      sql: `SELECT TOP ${topN} tbltemp_ItemsMain.ClientName, COUNT(*) AS OrderCount, SUM(tbltemp_ItemsMain.Amount) AS TotalSpent
            FROM tbltemp_ItemsMain
            WHERE tbltemp_ItemsMain.DocumentName = 'فاتورة مبيعات'
            GROUP BY tbltemp_ItemsMain.ClientName
            ORDER BY TotalSpent DESC`,
      explanation: `استعلام لعرض أكثر ${topN} عملاء شراءً من حيث إجمالي المبلغ المنفق`,
      performance_tips: ['استخدم فهرس على ClientName'],
      alternative_queries: [`استعلام مرتب حسب عدد الطلبات: ORDER BY OrderCount DESC`]
    };
  }

  return null;
}

interface QueryAnalysis {
  success: boolean;
  intent: string;
  entities: Record<string, any>;
  tables: string[];
  columns: string[];
  visualization: string;
  confidence: number;
}

interface SQLGeneration {
  success: boolean;
  sql: string;
  explanation: string;
}

interface ResultAnalysis {
  success: boolean;
  insights: string;
  patterns: string[];
  recommendations: string[];
}

// تحميل ملفات النيات والكيانات
async function loadIntentsAndEntities() {
  try {
    const intentsPath = path.join(process.cwd(), 'src/data/intents.json');
    const entitiesPath = path.join(process.cwd(), 'src/data/entities.json');
    const synonymsPath = path.join(process.cwd(), 'src/data/synonyms.json');
    const useCasesPath = path.join(process.cwd(), 'src/data/use-cases.json');

    const [intentsData, entitiesData, synonymsData, useCasesData] = await Promise.all([
      fs.readFile(intentsPath, 'utf-8'),
      fs.readFile(entitiesPath, 'utf-8'),
      fs.readFile(synonymsPath, 'utf-8'),
      fs.readFile(useCasesPath, 'utf-8')
    ]);

    const parsedUseCases = JSON.parse(useCasesData);

    return {
      intents: JSON.parse(intentsData),
      entities: JSON.parse(entitiesData),
      synonyms: JSON.parse(synonymsData),
      useCases: parsedUseCases.use_cases || []
    };
  } catch (error) {
    console.error('خطأ في تحميل ملفات النيات والكيانات:', error);
    return null;
  }
}

// تحميل مخطط قاعدة البيانات
async function loadDatabaseSchema() {
  try {
    const schemaPath = path.join(process.cwd(), 'src/data/database-schema.json');
    const schemaData = await fs.readFile(schemaPath, 'utf-8');
    return JSON.parse(schemaData);
  } catch (error) {
    console.error('خطأ في تحميل مخطط قاعدة البيانات:', error);
    return null;
  }
}

// دالة للبحث عن النيات المطابقة
function findMatchingIntents(query: string, intents: any[]): any[] {
  const queryLower = query.toLowerCase();
  const matchingIntents = [];

  for (const intent of intents) {
    // التحقق من وجود keywords قبل استخدامها
    if (!intent.keywords || !Array.isArray(intent.keywords)) {
      console.warn('⚠️ النية لا تحتوي على keywords:', intent.intent || intent.name);
      continue;
    }

    const score = intent.keywords.reduce((acc: number, keyword: string) => {
      if (queryLower.includes(keyword.toLowerCase())) {
        return acc + 1;
      }
      return acc;
    }, 0);

    if (score > 0) {
      matchingIntents.push({ ...intent, score });
    }
  }

  console.log('🎯 تم العثور على', matchingIntents.length, 'نيات مطابقة');
  return matchingIntents.sort((a, b) => b.score - a.score);
}

// دالة لاستخراج الكيانات من الاستعلام
function extractEntitiesFromQuery(query: string, entities: any, synonyms: any): any {
  const queryLower = query.toLowerCase();
  const extractedEntities: any = {};

  // استخراج الأرقام بطريقة ذكية ومرتبة حسب الأولوية

  // 1. البحث عن الكلمات المحددة أولاً (أعلى أولوية)
  if (queryLower.includes('منتجين') || queryLower.includes('منتجان')) {
    extractedEntities.top_n = 2;
    console.log('🔢 تم استخراج العدد 2 من "منتجين"');
  } else if (queryLower.includes('ثلاثة منتجات') || queryLower.includes('ثلاثة')) {
    extractedEntities.top_n = 3;
    console.log('🔢 تم استخراج العدد 3 من "ثلاثة"');
  } else {
    // 2. البحث عن الأرقام المكتوبة بالأرقام
    const numberMatches = query.match(/\d+/g);
    if (numberMatches) {
      const numbers = numberMatches.map(n => parseInt(n));
      if (numbers.length > 0) {
        extractedEntities.top_n = numbers[0];
        console.log(`🔢 تم استخراج العدد ${numbers[0]} من الأرقام`);
      }
    } else {
      // 3. البحث عن الكلمات الأخرى
      const wordNumbers: { [key: string]: number } = {
        'اثنين': 2,
        'أربعة': 4,
        'خمسة': 5,
        'ستة': 6,
        'سبعة': 7,
        'ثمانية': 8,
        'تسعة': 9,
        'عشرة': 10
      };

      for (const [word, number] of Object.entries(wordNumbers)) {
        if (queryLower.includes(word)) {
          extractedEntities.top_n = number;
          console.log(`🔢 تم استخراج العدد ${number} من الكلمة "${word}"`);
          break;
        }
      }
    }
  }

  // البحث في المرادفات
  for (const [column, synonymList] of Object.entries(synonyms)) {
    for (const synonym of synonymList as string[]) {
      if (queryLower.includes(synonym.toLowerCase())) {
        // تحديد نوع الكيان بناءً على العمود
        for (const [entityType, columns] of Object.entries(entities)) {
          if ((columns as string[]).includes(column)) {
            extractedEntities[entityType] = synonym;
            break;
          }
        }
      }
    }
  }

  return extractedEntities;
}

export async function analyzeQuery(query: string): Promise<QueryAnalysis> {
  try {
    console.log('🔍 بدء تحليل الاستعلام:', query);

    const data = await loadIntentsAndEntities();
    const schema = await loadDatabaseSchema();

    if (!data || !schema) {
      console.error('❌ فشل في تحميل ملفات البيانات');
      return {
        success: false,
        intent: '',
        entities: {},
        tables: [],
        columns: [],
        visualization: 'table',
        confidence: 0
      };
    }

    console.log('✅ تم تحميل ملفات البيانات بنجاح');
    console.log('📊 عدد النيات المتاحة:', data.intents.length);
    console.log('🏷️ عدد الكيانات المتاحة:', Object.keys(data.entities).length);

    // البحث عن النيات المطابقة أولاً
    const matchingIntents = findMatchingIntents(query, data.intents);
    console.log('🎯 النيات المطابقة:', matchingIntents.map((i: any) => i.intent));

    // استخراج الكيانات من الاستعلام
    const extractedEntities = extractEntitiesFromQuery(query, data.entities, data.synonyms);
    console.log('🏷️ الكيانات المستخرجة:', extractedEntities);

    const prompt = `
أنت محلل ذكي للاستعلامات التجارية باللغة العربية. مهمتك تحليل الاستعلام وإنتاج تحليل دقيق للغاية.

🔍 الاستعلام المطلوب تحليله:
"${query}"

📋 النيات المطابقة المحتملة:
${JSON.stringify(matchingIntents, null, 2)}

🏷️ الكيانات المستخرجة مسبقاً:
${JSON.stringify(extractedEntities, null, 2)}

📊 مخطط قاعدة البيانات:
${JSON.stringify(schema.tables, null, 2)}

قواعد التحليل المتقدم:
1. حدد الفئة الرئيسية للاستعلام (المنتجات، العملاء، المخازن، المبالغ، التواريخ، الفواتير، المحاسبة)
2. استخدم التصنيف الذكي للأعمدة لاختيار الأعمدة الأنسب
3. تعرف على الأرقام والكميات والفترات الزمنية بدقة
4. ميز بين أنواع التحليل المختلفة (ترتيب، مقارنة، اتجاهات، إحصائيات)
5. اختر نوع التصور الأنسب حسب طبيعة البيانات

يرجى تحليل الاستعلام وإرجاع النتيجة بصيغة JSON فقط بدون أي نص إضافي أو تنسيق markdown بالتنسيق التالي:
{
  "success": true,
  "intent": "اسم النية المناسبة",
  "category": "الفئة الرئيسية للاستعلام",
  "entities": {
    "entity_name": "القيمة المستخرجة"
  },
  "tables": ["أسماء الجداول المطلوبة"],
  "columns": ["أسماء الأعمدة المطلوبة مع أسماء الجداول"],
  "filters": {
    "date_range": "الفترة الزمنية إن وجدت",
    "conditions": ["الشروط الإضافية"]
  },
  "visualization": "نوع التصور المناسب (table, bar, line, pie)",
  "confidence": 0.95,
  "analysis_type": "نوع التحليل (ranking, comparison, trend, summary)"
}

أرجع JSON فقط بدون أي نص آخر.
`;

    const response = await groq.chat.completions.create({
      model: MODEL_NAME,
      messages: [
        {
          role: 'system',
          content: 'أنت محلل ذكي للاستعلامات باللغة العربية. تحلل الاستعلامات وتستخرج النيات والكيانات بدقة.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.1,
      max_tokens: 1000
    });

    const result = response.choices[0]?.message?.content;
    if (!result) {
      throw new Error('لم يتم الحصول على رد من النموذج اللغوي');
    }

    // محاولة تحليل الرد كـ JSON
    try {
      const analysis = extractAndParseJSON(result);
      return analysis;
    } catch (parseError) {
      console.error('خطأ في تحليل رد النموذج اللغوي:', parseError);
      return {
        success: false,
        intent: '',
        entities: {},
        tables: [],
        columns: [],
        visualization: 'table',
        confidence: 0
      };
    }

  } catch (error) {
    console.error('خطأ في تحليل الاستعلام:', error);
    return {
      success: false,
      intent: '',
      entities: {},
      tables: [],
      columns: [],
      visualization: 'table',
      confidence: 0
    };
  }
}

export async function generateSQL(queryAnalysis: QueryAnalysis): Promise<SQLGeneration> {
  try {
    console.log('🔧 بدء توليد استعلام SQL...');
    console.log('📊 تحليل الاستعلام:', queryAnalysis);

    // التحقق من الاستعلامات الشائعة أولاً (fallback)
    const commonQuery = generateCommonQuery(queryAnalysis);
    if (commonQuery) {
      console.log('⚡ استخدام استعلام شائع محفوظ');
      return commonQuery;
    }

    const schema = await loadDatabaseSchema();
    const data = await loadIntentsAndEntities();

    if (!schema || !data) {
      console.error('❌ فشل في تحميل البيانات المطلوبة');
      return {
        success: false,
        sql: '',
        explanation: 'فشل في تحميل البيانات المطلوبة'
      };
    }

    // البحث عن حالات استخدام مشابهة
    const similarUseCases = data.useCases.filter((useCase: any) => {
      // البحث في اسم حالة الاستخدام
      if (useCase.name && useCase.name.includes('منتج') && useCase.name.includes('مبيع')) {
        return true;
      }

      // البحث في الأسئلة المثالية
      if (useCase.example_questions) {
        return useCase.example_questions.some((question: string) =>
          question.includes('منتج') && (question.includes('مبيع') || question.includes('أكثر'))
        );
      }

      return false;
    });

    console.log('📋 حالات الاستخدام المشابهة:', similarUseCases.length);

    const prompt = `
أنت خبير في كتابة استعلامات SQL Server متخصص في التحليلات التجارية. مهمتك إنشاء استعلام SQL دقيق وفعال ومحسن للأداء.

تحليل الاستعلام المتقدم:
${JSON.stringify(queryAnalysis, null, 2)}

مخطط قاعدة البيانات (الأعمدة المهمة):
الجداول المتاحة: tbltemp_ItemsMain, tbltemp_Inv_MainInvoice

الأعمدة المهمة في tbltemp_ItemsMain:
- ItemName (اسم المنتج)
- Quantity (الكمية)
- Amount (المبلغ)
- UnitPrice (سعر الوحدة)
- CategoryName (اسم الفئة)
- DocumentName (نوع الوثيقة)
- TheDate (التاريخ)

الأعمدة المهمة في tbltemp_Inv_MainInvoice:
- ItemName (اسم المنتج)
- Quantity (الكمية)
- TotalAmount (المبلغ الإجمالي)
- UnitPrice (سعر الوحدة)
- DocumentName (نوع الوثيقة)
- TheDate (التاريخ)

أمثلة حالات الاستخدام المشابهة (أول 3 فقط):
${JSON.stringify(similarUseCases.slice(0, 3), null, 2)}

قواعد إنشاء SQL المتقدم:
1. استخدم التصنيف الذكي لاختيار الأعمدة الأنسب
2. طبق الفلاتر الزمنية بدقة (هذا الشهر، الشهر الماضي، الربع، السنة)
3. استخدم الشروط المناسبة للمستندات (فاتورة مبيعات، فاتورة مشتريات)
4. أضف التجميع والترتيب المناسب حسب نوع التحليل
5. استخدم الدوال المناسبة (SUM, COUNT, AVG, MAX, MIN)
6. أضف DISTINCT عند الحاجة لتجنب التكرار
7. استخدم JOIN إذا كان هناك أكثر من جدول
8. **مهم جداً**: إذا كان هناك top_n في الكيانات، استخدم TOP مع هذا الرقم بالضبط

تنبيه مهم جداً حول أسماء الأعمدة:
- في جدول tbltemp_ItemsMain: استخدم عمود "Amount" للمبالغ (وليس TotalAmount)
- في جدول tbltemp_Inv_MainInvoice: استخدم عمود "TotalAmount" للمبالغ
- لا تخلط بين الجدولين والأعمدة! تحقق من المخطط أعلاه
- تأكد من أن جميع الأعمدة المستخدمة موجودة فعلاً في الجداول
- استخدم أسماء الأعمدة الصحيحة: ItemName, ClientName, Amount, Quantity

تنبيه مهم جداً حول الأعمدة المتشابهة:
- ClientID موجود في كلا الجدولين: tbltemp_ItemsMain.ClientID و tbltemp_Inv_MainInvoice.ClientID
- عند استخدام JOIN، يجب تحديد الجدول: مثل tbltemp_ItemsMain.ClientID
- ClientName موجود فقط في tbltemp_ItemsMain، لذا استخدم tbltemp_ItemsMain.ClientName
- لتجنب الأخطاء، استخدم دائماً اسم الجدول.اسم العمود في الاستعلامات المعقدة

تنبيه مهم حول الكمية المطلوبة:
- إذا كان في الكيانات top_n = 2، استخدم TOP 2 وليس TOP 10
- إذا كان في الكيانات top_n = 3، استخدم TOP 3 وليس TOP 10
- إذا لم يكن هناك top_n، استخدم TOP 10 كافتراضي

أمثلة للفلاتر الزمنية:
- هذا الشهر: WHERE YEAR(TheDate) = YEAR(GETDATE()) AND MONTH(TheDate) = MONTH(GETDATE())
- الشهر الماضي: WHERE YEAR(TheDate) = YEAR(DATEADD(MONTH, -1, GETDATE())) AND MONTH(TheDate) = MONTH(DATEADD(MONTH, -1, GETDATE()))
- هذا العام: WHERE YEAR(TheDate) = YEAR(GETDATE())
- الربع الحالي: WHERE YEAR(TheDate) = YEAR(GETDATE()) AND DATEPART(QUARTER, TheDate) = DATEPART(QUARTER, GETDATE())

يرجى إنشاء استعلام SQL مناسب وإرجاع النتيجة كـ JSON صحيح فقط بدون أي نص إضافي أو markdown:

{
  "success": true,
  "sql": "استعلام SQL كامل وصحيح ومحسن",
  "explanation": "شرح مفصل للاستعلام باللغة العربية",
  "performance_tips": ["نصائح لتحسين الأداء"],
  "alternative_queries": ["استعلامات بديلة إن وجدت"]
}

مهم جداً: أرجع JSON فقط بدون أي تفسيرات أو عناوين أو markdown أو نص إضافي.

ملاحظات مهمة:
- استخدم أسماء الجداول والأعمدة الصحيحة من المخطط
- تأكد من صحة صيغة SQL Server
- استخدم الفلاتر والشروط المناسبة حسب السياق
- أضف ORDER BY و TOP/LIMIT إذا كان مناسباً
- استخدم GROUP BY للتجميع عند الحاجة
- تجنب SELECT * واستخدم أسماء الأعمدة المحددة
- استخدم الفهارس المناسبة للأداء الأمثل
- استخدم دائماً اسم الجدول.اسم العمود لتجنب الأخطاء (مثل: tbltemp_ItemsMain.ClientID)

أمثلة على استعلامات SQL صحيحة:
1. أكثر المنتجات مبيعاً:
   SELECT TOP 5 tbltemp_ItemsMain.ItemName, SUM(tbltemp_ItemsMain.Quantity) AS TotalQuantity
   FROM tbltemp_ItemsMain
   WHERE tbltemp_ItemsMain.DocumentName = 'فاتورة مبيعات'
   GROUP BY tbltemp_ItemsMain.ItemName
   ORDER BY TotalQuantity DESC

2. أكثر العملاء شراءً:
   SELECT TOP 5 tbltemp_ItemsMain.ClientName, SUM(tbltemp_ItemsMain.Amount) AS TotalSpent
   FROM tbltemp_ItemsMain
   WHERE tbltemp_ItemsMain.DocumentName = 'فاتورة مبيعات'
   GROUP BY tbltemp_ItemsMain.ClientName
   ORDER BY TotalSpent DESC
`;

    const response = await groq.chat.completions.create({
      model: MODEL_NAME,
      messages: [
        {
          role: 'system',
          content: 'أنت خبير في SQL Server وتكتب استعلامات دقيقة وفعالة باللغة العربية.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.1,
      max_tokens: 1500
    });

    const result = response.choices[0]?.message?.content;
    if (!result) {
      throw new Error('لم يتم الحصول على رد من النموذج اللغوي');
    }

    try {
      const sqlGeneration = extractAndParseJSON(result);
      return sqlGeneration;
    } catch (parseError) {
      console.error('خطأ في تحليل رد النموذج اللغوي:', parseError);
      return {
        success: false,
        sql: '',
        explanation: 'فشل في تحليل رد النموذج اللغوي'
      };
    }

  } catch (error) {
    console.error('خطأ في توليد SQL:', error);
    return {
      success: false,
      sql: '',
      explanation: 'فشل في توليد استعلام SQL'
    };
  }
}

// دالة لتحسين الاستعلامات باستخدام التصنيف الذكي
export async function enhanceQueryWithIntelligentClassification(query: string, schema: any): Promise<any> {
  try {
    // استخدام التصنيف الذكي لتحسين فهم الاستعلام
    if (schema.intelligentClassification) {
      const classification = schema.intelligentClassification;

      // تحديد الفئة الأنسب للاستعلام
      let detectedCategory = null;
      let relevantColumns = [];

      // تحليل الكلمات المفتاحية في الاستعلام
      const queryLower = query.toLowerCase();

      if (queryLower.includes('منتج') || queryLower.includes('صنف') || queryLower.includes('سلعة')) {
        detectedCategory = 'المنتجات';
        relevantColumns = classification.المنتجات.columns;
      } else if (queryLower.includes('عميل') || queryLower.includes('زبون') || queryLower.includes('موزع')) {
        detectedCategory = 'العملاء';
        relevantColumns = classification.العملاء.columns;
      } else if (queryLower.includes('فرع') || queryLower.includes('مخزن') || queryLower.includes('مستودع')) {
        detectedCategory = 'المخازن';
        relevantColumns = classification.المخازن.columns;
      } else if (queryLower.includes('مبلغ') || queryLower.includes('سعر') || queryLower.includes('مبيعات')) {
        detectedCategory = 'المبالغ';
        relevantColumns = classification.المبالغ.columns;
      } else if (queryLower.includes('تاريخ') || queryLower.includes('شهر') || queryLower.includes('سنة')) {
        detectedCategory = 'التواريخ';
        relevantColumns = classification.التواريخ.columns;
      } else if (queryLower.includes('فاتورة') || queryLower.includes('مستند')) {
        detectedCategory = 'الفواتير';
        relevantColumns = classification.الفواتير.columns;
      }

      return {
        detectedCategory,
        relevantColumns,
        enhancedQuery: query,
        suggestions: generateQuerySuggestions(detectedCategory, relevantColumns)
      };
    }

    return { detectedCategory: null, relevantColumns: [], enhancedQuery: query, suggestions: [] };
  } catch (error) {
    console.error('خطأ في تحسين الاستعلام:', error);
    return { detectedCategory: null, relevantColumns: [], enhancedQuery: query, suggestions: [] };
  }
}

// دالة لتوليد اقتراحات الاستعلامات
function generateQuerySuggestions(category: string | null, columns: string[]): string[] {
  if (!category || columns.length === 0) return [];

  const suggestions = [];

  switch (category) {
    case 'المنتجات':
      suggestions.push('أعرض لي أكثر 10 منتجات مبيعاً');
      suggestions.push('ما هي المنتجات الأكثر ربحية؟');
      suggestions.push('تحليل مبيعات المنتجات حسب الفئة');
      break;
    case 'العملاء':
      suggestions.push('أكثر العملاء شراءً هذا الشهر');
      suggestions.push('تحليل سلوك العملاء');
      suggestions.push('العملاء الجدد مقابل العملاء المتكررين');
      break;
    case 'المخازن':
      suggestions.push('أداء الفروع المختلفة');
      suggestions.push('مقارنة مبيعات الفروع');
      suggestions.push('تحليل المخزون حسب الفرع');
      break;
    case 'المبالغ':
      suggestions.push('إجمالي المبيعات هذا الشهر');
      suggestions.push('تحليل الأرباح والخسائر');
      suggestions.push('مقارنة الإيرادات بالفترات السابقة');
      break;
    case 'التواريخ':
      suggestions.push('اتجاهات المبيعات الشهرية');
      suggestions.push('مقارنة الأداء السنوي');
      suggestions.push('تحليل الموسمية');
      break;
    case 'الفواتير':
      suggestions.push('تحليل أنواع الفواتير');
      suggestions.push('إحصائيات المستندات');
      suggestions.push('تتبع الفواتير المعلقة');
      break;
  }

  return suggestions;
}

// دالة لحساب إحصائيات متقدمة للبيانات
function calculateAdvancedStats(data: any[]): any {
  if (!data || data.length === 0) {
    return { message: 'لا توجد بيانات للتحليل' };
  }

  const stats: any = {
    totalRows: data.length,
    columns: Object.keys(data[0]),
    numericColumns: [],
    textColumns: [],
    summary: {}
  };

  // تحليل أنواع الأعمدة
  for (const column of stats.columns) {
    const firstValue = data[0][column];
    if (typeof firstValue === 'number' || !isNaN(Number(firstValue))) {
      stats.numericColumns.push(column);

      // حساب إحصائيات للأعمدة الرقمية
      const values = data.map(row => Number(row[column])).filter(val => !isNaN(val));
      if (values.length > 0) {
        stats.summary[column] = {
          min: Math.min(...values),
          max: Math.max(...values),
          avg: values.reduce((a, b) => a + b, 0) / values.length,
          sum: values.reduce((a, b) => a + b, 0),
          count: values.length
        };
      }
    } else {
      stats.textColumns.push(column);

      // حساب إحصائيات للأعمدة النصية
      const uniqueValues = [...new Set(data.map(row => row[column]))];
      stats.summary[column] = {
        uniqueCount: uniqueValues.length,
        mostCommon: findMostCommon(data.map(row => row[column])),
        samples: uniqueValues.slice(0, 5)
      };
    }
  }

  return stats;
}

// دالة للعثور على القيمة الأكثر تكراراً
function findMostCommon(arr: any[]): any {
  const frequency: any = {};
  let maxCount = 0;
  let mostCommon = null;

  for (const item of arr) {
    frequency[item] = (frequency[item] || 0) + 1;
    if (frequency[item] > maxCount) {
      maxCount = frequency[item];
      mostCommon = item;
    }
  }

  return { value: mostCommon, count: maxCount };
}

export async function analyzeResults(originalQuery: string, data: any[], queryAnalysis: QueryAnalysis): Promise<ResultAnalysis> {
  try {
    console.log('📊 بدء تحليل النتائج...');
    console.log('📈 عدد الصفوف:', data.length);

    // حساب إحصائيات متقدمة
    const stats = calculateAdvancedStats(data);
    console.log('📊 الإحصائيات المتقدمة:', stats);

    const prompt = `
أنت محلل بيانات خبير ومستشار أعمال متخصص في التحليلات التجارية المتقدمة. مهمتك تحليل النتائج وتقديم رؤى عميقة وتوصيات استراتيجية.

🔍 الاستعلام الأصلي: "${originalQuery}"

🎯 تحليل الاستعلام:
${JSON.stringify(queryAnalysis, null, 2)}

📊 البيانات (عينة من أول 10 صفوف):
${JSON.stringify(data.slice(0, 10), null, 2)}

📈 الإحصائيات المتقدمة:
${JSON.stringify(stats, null, 2)}

📋 معلومات البيانات:
- إجمالي عدد الصفوف: ${data.length}
- عدد الأعمدة: ${data.length > 0 ? Object.keys(data[0]).length : 0}
- أسماء الأعمدة: ${data.length > 0 ? Object.keys(data[0]).join(', ') : 'لا توجد بيانات'}

🎯 قواعد التحليل المتقدم والذكي:
1. 📊 حلل البيانات حسب الفئة والسياق التجاري
2. 📈 اكتشف الأنماط والاتجاهات والانحرافات المهمة
3. 🔍 قدم مقارنات ذكية ومعايير الأداء
4. 📋 احسب النسب والمعدلات والمؤشرات الرئيسية
5. 💡 قدم توصيات استراتيجية قابلة للتنفيذ
6. 🏢 اربط النتائج بالأهداف التجارية والربحية
7. ⚠️ حدد المخاطر والفرص والتحديات
8. 📅 اقترح خطط عمل زمنية محددة
9. 💰 قدر التأثير المالي للتوصيات
10. 🎯 حدد مؤشرات الأداء الرئيسية للمتابعة

أرجع النتيجة بصيغة JSON فقط بدون أي نص إضافي أو تنسيق markdown بالتنسيق التالي:
{
  "success": true,
  "executive_summary": "ملخص تنفيذي شامل للنتائج الرئيسية",
  "insights": "تحليل مفصل وذكي للنتائج مع السياق التجاري والاستراتيجي",
  "key_findings": [
    "النتيجة الرئيسية الأولى مع الأرقام والنسب",
    "النتيجة الرئيسية الثانية مع المقارنات",
    "النتيجة الرئيسية الثالثة مع التوجهات"
  ],
  "patterns_and_trends": [
    "نمط مهم مع التفسير والأسباب",
    "اتجاه واضح مع التوقعات المستقبلية",
    "انحراف أو استثناء يحتاج انتباه"
  ],
  "performance_metrics": {
    "total_records": ${data.length},
    "top_performer": "أعلى أداء مع القيمة والنسبة",
    "average_performance": "متوسط الأداء مع المقارنة",
    "growth_rate": "معدل النمو أو التغيير",
    "market_share": "الحصة السوقية إن أمكن"
  },
  "strategic_recommendations": [
    {
      "title": "توصية استراتيجية رئيسية",
      "description": "وصف مفصل مع الخطوات العملية",
      "priority": "عالية",
      "timeline": "الإطار الزمني للتنفيذ",
      "expected_impact": "التأثير المتوقع بالأرقام",
      "resources_needed": "الموارد المطلوبة"
    }
  ],
  "business_opportunities": [
    "فرصة تجارية محددة مع التبرير",
    "مجال للتحسين مع الخطوات العملية"
  ],
  "risk_factors": [
    "مخاطر محتملة مع خطط التخفيف",
    "تحديات يجب معالجتها"
  ],
  "kpis_to_monitor": [
    "مؤشر أداء رئيسي للمتابعة",
    "معيار نجاح قابل للقياس"
  ],
  "next_actions": [
    "إجراء فوري مطلوب",
    "خطوة تالية مع المسؤولية والتوقيت"
  ]
}

💼 متطلبات التحليل الاحترافي:
✅ ملخص تنفيذي شامل للقيادة العليا
✅ تحليل عميق للأنماط والاتجاهات مع الأسباب
✅ مقارنات ذكية مع معايير الصناعة
✅ توصيات استراتيجية مع خطط التنفيذ
✅ رؤى تجارية مبنية على البيانات
✅ تحليل شامل للفرص والمخاطر
✅ خطة عمل واضحة مع الأولويات والمواعيد

أرجع JSON فقط بدون أي نص آخر.
`;

    const response = await groq.chat.completions.create({
      model: MODEL_NAME,
      messages: [
        {
          role: 'system',
          content: 'أنت محلل بيانات خبير تقدم رؤى ذكية وتوصيات عملية باللغة العربية.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.3,
      max_tokens: 2000
    });

    const result = response.choices[0]?.message?.content;
    if (!result) {
      throw new Error('لم يتم الحصول على رد من النموذج اللغوي');
    }

    try {
      const analysis = extractAndParseJSON(result);
      return analysis;
    } catch (parseError) {
      console.error('خطأ في تحليل رد النموذج اللغوي:', parseError);
      return {
        success: true,
        insights: result, // استخدام النص كما هو إذا فشل التحليل
        patterns: [],
        recommendations: []
      };
    }

  } catch (error) {
    console.error('خطأ في تحليل النتائج:', error);
    return {
      success: false,
      insights: 'فشل في تحليل النتائج',
      patterns: [],
      recommendations: []
    };
  }
}
