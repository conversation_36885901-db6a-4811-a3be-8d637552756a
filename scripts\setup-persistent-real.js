async function setupPersistentReal() {
  try {
    console.log('🚀 بدء إعداد نظام Persistent Vector Database الحقيقي...');
    console.log('📊 سيتم تحويل ملف rga.txt إلى vectors وحفظها في قاعدة بيانات دائمة');

    // تحميل خدمة Persistent RAG
    const { 
      initializePersistentRAG, 
      indexPersistentKnowledgeBase, 
      searchPersistentKnowledge, 
      getPersistentStats,
      loadPersistentIndex
    } = await import('../src/lib/persistent-rag-service.js');

    console.log('✅ تم تحميل خدمة Persistent RAG');

    // 1. تهيئة النظام
    console.log('\n1️⃣ تهيئة Persistent Vector Database...');
    await initializePersistentRAG();

    // 2. فهرسة قاعدة المعرفة وتحويلها إلى vectors
    console.log('\n2️⃣ تحويل rga.txt إلى vectors وفهرسة في قاعدة البيانات...');
    console.log('⏳ هذه العملية قد تستغرق بعض الوقت...');
    
    const startTime = Date.now();
    await indexPersistentKnowledgeBase();
    const indexTime = Date.now() - startTime;
    
    console.log(`⏱️ تم الانتهاء من الفهرسة في ${indexTime} مللي ثانية`);

    // 3. التحقق من حفظ قاعدة البيانات
    console.log('\n3️⃣ التحقق من قاعدة البيانات المحفوظة...');
    const fs = require('fs').promises;
    const path = require('path');
    
    const dbPath = path.join(process.cwd(), 'persistent_database');
    try {
      const files = await fs.readdir(dbPath);
      console.log(`📁 ملفات قاعدة البيانات: ${files.length} ملف`);
      console.log(`📋 الملفات: ${files.join(', ')}`);
      
      // فحص ملف قاعدة البيانات الرئيسي
      const mainDbFile = path.join(dbPath, 'persistent_rag.json');
      try {
        const stats = await fs.stat(mainDbFile);
        console.log(`📄 حجم قاعدة البيانات: ${(stats.size / 1024).toFixed(2)} KB`);
        console.log(`📅 تاريخ الإنشاء: ${stats.birthtime.toLocaleString('ar-SA')}`);
        console.log(`📅 آخر تعديل: ${stats.mtime.toLocaleString('ar-SA')}`);
        
        // قراءة عينة من البيانات
        const sampleData = JSON.parse(await fs.readFile(mainDbFile, 'utf-8'));
        console.log(`📊 عدد المستندات: ${sampleData.documents?.length || 0}`);
        console.log(`📊 عدد Embeddings: ${sampleData.embeddings?.length || 0}`);
        console.log(`📊 أبعاد Embedding: ${sampleData.dimension || 'غير محدد'}`);
        console.log(`📊 تاريخ الحفظ: ${sampleData.saved_at || 'غير محدد'}`);
      } catch (error) {
        console.log('❌ خطأ في قراءة ملف قاعدة البيانات:', error.message);
      }
    } catch (error) {
      console.log('❌ خطأ في قراءة مجلد قاعدة البيانات:', error.message);
    }

    // 4. الحصول على الإحصائيات
    console.log('\n4️⃣ الحصول على إحصائيات النظام...');
    const stats = await getPersistentStats();
    console.log('📊 إحصائيات Persistent Vector Database:');
    console.log(JSON.stringify(stats, null, 2));

    // 5. اختبار تحميل قاعدة البيانات
    console.log('\n5️⃣ اختبار تحميل قاعدة البيانات من القرص...');
    try {
      await loadPersistentIndex();
      console.log('✅ تم تحميل قاعدة البيانات بنجاح من القرص');
    } catch (error) {
      console.log('❌ خطأ في تحميل قاعدة البيانات:', error.message);
    }

    // 6. اختبار البحث المتقدم
    console.log('\n6️⃣ اختبار البحث في Persistent Vector Database...');
    
    const testQueries = [
      'أكثر المنتجات مبيعاً',
      'تفاصيل جدول المنتجات',
      'العلاقات بين الجداول',
      'أمثلة استعلامات SQL',
      'مبيعات الفرع الرئيسي',
      'تحليل العملاء',
      'مخزون المنتجات',
      'فواتير المبيعات'
    ];

    for (const query of testQueries) {
      console.log(`\n🔍 اختبار: "${query}"`);
      const searchStart = Date.now();
      const results = await searchPersistentKnowledge(query, 3, 0.1);
      const searchTime = Date.now() - searchStart;
      
      if (results.length > 0) {
        console.log(`   ✅ تم العثور على ${results.length} نتائج في ${searchTime} مللي ثانية`);
        console.log(`   📊 أعلى تشابه: ${(results[0].similarity * 100).toFixed(1)}%`);
        console.log(`   📄 نوع المحتوى: ${results[0].metadata.content_type || 'غير محدد'}`);
        console.log(`   📏 نقاط التشابه: ${results[0].score?.toFixed(4) || 'غير محدد'}`);
        console.log(`   📝 بداية المحتوى: ${results[0].content.substring(0, 80)}...`);
        
        // عرض metadata إضافية
        if (results[0].metadata.mentioned_tables) {
          console.log(`   🗂️ الجداول المذكورة: ${results[0].metadata.mentioned_tables.join(', ')}`);
        }
        if (results[0].metadata.keywords) {
          console.log(`   🔑 الكلمات المفتاحية: ${results[0].metadata.keywords.slice(0, 3).join(', ')}`);
        }
        if (results[0].metadata.importance_score) {
          console.log(`   ⭐ درجة الأهمية: ${results[0].metadata.importance_score.toFixed(2)}`);
        }
      } else {
        console.log(`   ❌ لم يتم العثور على نتائج`);
      }
    }

    // 7. اختبار الأداء
    console.log('\n7️⃣ اختبار أداء Persistent Vector Database...');
    const performanceQuery = 'أكثر المنتجات مبيعاً';
    const iterations = 10;
    const times = [];

    for (let i = 0; i < iterations; i++) {
      const startTime = Date.now();
      await searchPersistentKnowledge(performanceQuery, 5, 0.1);
      const endTime = Date.now();
      times.push(endTime - startTime);
      console.log(`   ⏱️ التكرار ${i + 1}: ${endTime - startTime} مللي ثانية`);
    }

    const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);
    
    console.log(`   📊 متوسط الوقت: ${avgTime.toFixed(2)} مللي ثانية`);
    console.log(`   📊 أسرع بحث: ${minTime} مللي ثانية`);
    console.log(`   📊 أبطأ بحث: ${maxTime} مللي ثانية`);

    // 8. اختبار جودة النتائج
    console.log('\n8️⃣ اختبار جودة النتائج...');
    const qualityTests = [
      {
        query: 'جدول المنتجات الرئيسي',
        expectedType: 'table_description',
        expectedKeywords: ['tbltemp_ItemsMain', 'ItemName'],
        minSimilarity: 0.2
      },
      {
        query: 'مقارنة مبيعات الفروع',
        expectedType: 'intent',
        expectedKeywords: ['مقارنة', 'مبيعات', 'فرع'],
        minSimilarity: 0.15
      },
      {
        query: 'استعلام SQL للعملاء',
        expectedKeywords: ['SQL', 'عميل', 'استعلام'],
        minSimilarity: 0.15
      },
      {
        query: 'تحليل المخزون',
        expectedKeywords: ['مخزون', 'تحليل'],
        minSimilarity: 0.1
      }
    ];

    for (const test of qualityTests) {
      console.log(`\n🎯 اختبار جودة: "${test.query}"`);
      const results = await searchPersistentKnowledge(test.query, 3, 0.05);
      
      if (results.length > 0) {
        const topResult = results[0];
        
        // اختبار نوع المحتوى
        if (test.expectedType) {
          if (topResult.metadata.content_type === test.expectedType) {
            console.log(`   ✅ نوع المحتوى صحيح: ${test.expectedType}`);
          } else {
            console.log(`   ⚠️ نوع المحتوى غير متوقع: ${topResult.metadata.content_type}`);
          }
        }
        
        // اختبار الكلمات المفتاحية
        const hasKeywords = test.expectedKeywords.some(keyword => 
          topResult.content.toLowerCase().includes(keyword.toLowerCase())
        );
        
        if (hasKeywords) {
          console.log(`   ✅ يحتوي على الكلمات المفتاحية المتوقعة`);
        } else {
          console.log(`   ⚠️ لا يحتوي على الكلمات المفتاحية المتوقعة`);
        }
        
        // اختبار التشابه
        if (topResult.similarity >= test.minSimilarity) {
          console.log(`   ✅ نقاط التشابه مقبولة: ${(topResult.similarity * 100).toFixed(1)}%`);
        } else {
          console.log(`   ⚠️ نقاط التشابه منخفضة: ${(topResult.similarity * 100).toFixed(1)}%`);
        }
      } else {
        console.log(`   ❌ لم يتم العثور على نتائج`);
      }
    }

    // 9. اختبار استمرارية البيانات
    console.log('\n9️⃣ اختبار استمرارية البيانات...');
    try {
      // إعادة تحميل النظام من الصفر
      const { 
        initializePersistentRAG: initNew, 
        loadPersistentIndex: loadNew,
        searchPersistentKnowledge: searchNew
      } = await import('../src/lib/persistent-rag-service.js');
      
      await initNew();
      await loadNew();
      const persistenceResults = await searchNew('أكثر المنتجات مبيعاً', 2);
      
      if (persistenceResults.length > 0) {
        console.log('   ✅ البيانات محفوظة بشكل دائم ويمكن تحميلها');
        console.log(`   📊 تم العثور على ${persistenceResults.length} نتائج بعد إعادة التحميل`);
      } else {
        console.log('   ❌ فشل في تحميل البيانات المحفوظة');
      }
    } catch (error) {
      console.log('   ❌ خطأ في اختبار الاستمرارية:', error.message);
    }

    // 10. ملخص النتائج النهائي
    console.log('\n🔟 ملخص الإعداد النهائي...');
    console.log('🎉 تم إعداد Persistent Vector Database بنجاح!');
    console.log('📋 الملخص الشامل:');
    console.log(`   - إجمالي المستندات: ${stats.total_documents}`);
    console.log(`   - نوع Embedding: ${stats.embedding_type || 'persistent_advanced'}`);
    console.log(`   - أبعاد Embedding: ${stats.embedding_dimension || 512}`);
    console.log(`   - مسار قاعدة البيانات: ${stats.database_path || './persistent_database'}`);
    console.log(`   - مصدر البيانات: ${stats.file_source || 'rga.txt'}`);
    console.log(`   - حجم الملف الأصلي: ${stats.file_size ? (stats.file_size / 1024).toFixed(2) + ' KB' : 'غير محدد'}`);
    console.log(`   - متوسط وقت البحث: ${avgTime.toFixed(2)} مللي ثانية`);
    console.log(`   - حالة النظام: ${stats.status}`);
    console.log(`   - ملفات قاعدة البيانات موجودة: ${stats.files_exist ? 'نعم' : 'لا'}`);

    console.log('\n✅ نظام Persistent Vector Database جاهز للاستخدام!');
    console.log('📁 قاعدة البيانات محفوظة بشكل دائم في مجلد: ./persistent_database');
    console.log('🔗 يمكن الآن ربطه مع النموذج اللغوي لتوليد استعلامات SQL بدقة عالية');
    console.log('🚀 النظام يدعم البحث السريع والدقيق في المعرفة المفهرسة');
    console.log('💾 البيانات محفوظة بشكل دائم ولا تحتاج إعادة فهرسة');

    // 11. نصائح للاستخدام
    console.log('\n📝 نصائح للاستخدام:');
    console.log('   - يمكن تشغيل النظام مباشرة بدون إعادة فهرسة');
    console.log('   - لإعادة الفهرسة، احذف مجلد persistent_database وأعد تشغيل الإعداد');
    console.log('   - النظام يدعم البحث بالعربية والإنجليزية');
    console.log('   - كلما كان الاستعلام أكثر تحديداً، كانت النتائج أدق');

  } catch (error) {
    console.error('❌ خطأ في إعداد Persistent Vector Database:', error);
    console.error('📋 تفاصيل الخطأ:', error.stack);
    process.exit(1);
  }
}

// تشغيل الإعداد
if (require.main === module) {
  setupPersistentReal();
}

module.exports = { setupPersistentReal };
