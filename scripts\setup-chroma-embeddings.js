async function setupChromaEmbeddings() {
  try {
    console.log('🚀 بدء إعداد نظام Chroma مع Embeddings حقيقية...');

    // تحميل خدمة Chroma RAG
    const { 
      initializeChromaRAG, 
      indexKnowledgeBaseWithEmbeddings, 
      searchWithEmbeddings, 
      getChromaStats 
    } = await import('../src/lib/chroma-rag-service.js');

    console.log('✅ تم تحميل خدمة Chroma RAG');

    // 1. تهيئة قاعدة البيانات
    console.log('\n1️⃣ تهيئة Chroma Database...');
    await initializeChromaRAG();

    // 2. فهرسة قاعدة المعرفة مع embeddings
    console.log('\n2️⃣ فهرسة قاعدة المعرفة مع embeddings...');
    await indexKnowledgeBaseWithEmbeddings();

    // 3. الحصول على الإحصائيات
    console.log('\n3️⃣ الحصول على إحصائيات النظام...');
    const stats = await getChromaStats();
    console.log('📊 إحصائيات Chroma:', JSON.stringify(stats, null, 2));

    // 4. اختبار البحث مع embeddings
    console.log('\n4️⃣ اختبار البحث مع embeddings...');
    
    const testQueries = [
      'أكثر المنتجات مبيعاً',
      'تفاصيل جدول المنتجات',
      'العلاقات بين الجداول',
      'أمثلة استعلامات SQL'
    ];

    for (const query of testQueries) {
      console.log(`\n🔍 اختبار: "${query}"`);
      const results = await searchWithEmbeddings(query, 3);
      
      if (results.length > 0) {
        console.log(`   ✅ تم العثور على ${results.length} نتائج`);
        console.log(`   📊 أعلى تشابه: ${(results[0].similarity * 100).toFixed(1)}%`);
        console.log(`   📄 نوع المحتوى: ${results[0].metadata.content_type || 'غير محدد'}`);
        console.log(`   📏 المسافة: ${results[0].distance?.toFixed(4) || 'غير محدد'}`);
        console.log(`   📝 بداية المحتوى: ${results[0].content.substring(0, 80)}...`);
      } else {
        console.log(`   ❌ لم يتم العثور على نتائج`);
      }
    }

    // 5. اختبار الأداء
    console.log('\n5️⃣ اختبار الأداء...');
    const performanceQuery = 'أكثر المنتجات مبيعاً';
    const iterations = 3;
    const times = [];

    for (let i = 0; i < iterations; i++) {
      const startTime = Date.now();
      await searchWithEmbeddings(performanceQuery, 5);
      const endTime = Date.now();
      times.push(endTime - startTime);
      console.log(`   ⏱️ التكرار ${i + 1}: ${endTime - startTime} مللي ثانية`);
    }

    const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
    console.log(`   📊 متوسط الوقت: ${avgTime.toFixed(2)} مللي ثانية`);

    // 6. ملخص النتائج
    console.log('\n6️⃣ ملخص الإعداد...');
    console.log('🎉 تم إعداد نظام Chroma مع Embeddings بنجاح!');
    console.log('📋 الملخص:');
    console.log(`   - إجمالي المستندات: ${stats.total_documents}`);
    console.log(`   - نموذج Embedding: ${stats.embedding_model || 'sentence-transformers/all-MiniLM-L6-v2'}`);
    console.log(`   - مسار قاعدة البيانات: ${stats.database_path || './chroma_db'}`);
    console.log(`   - نوع النظام: ${stats.type}`);
    console.log(`   - متوسط وقت البحث: ${avgTime.toFixed(2)} مللي ثانية`);
    console.log(`   - حالة النظام: ${stats.status}`);

    console.log('\n✅ النظام جاهز للاستخدام مع embeddings حقيقية!');
    console.log('📁 قاعدة البيانات محفوظة في مجلد: ./chroma_db');

  } catch (error) {
    console.error('❌ خطأ في إعداد نظام Chroma:', error);
    process.exit(1);
  }
}

// تشغيل الإعداد
if (require.main === module) {
  setupChromaEmbeddings();
}

module.exports = { setupChromaEmbeddings };
