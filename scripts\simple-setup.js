const fs = require('fs').promises;
const path = require('path');

// تحويل النص إلى chunks صغيرة
function splitTextIntoChunks(text, maxChunkSize = 1000) {
  const chunks = [];
  const lines = text.split('\n');
  let currentChunk = '';

  for (const line of lines) {
    if (currentChunk.length + line.length > maxChunkSize && currentChunk.length > 0) {
      chunks.push(currentChunk.trim());
      currentChunk = line;
    } else {
      currentChunk += (currentChunk ? '\n' : '') + line;
    }
  }

  if (currentChunk.trim()) {
    chunks.push(currentChunk.trim());
  }

  return chunks;
}

// استخراج metadata من النص
function extractMetadata(text, chunkIndex) {
  const metadata = {
    chunk_index: chunkIndex,
    length: text.length,
    type: 'knowledge_base'
  };

  // استخراج نوع المحتوى
  if (text.includes('الجدول')) {
    metadata.content_type = 'table_description';
    
    // استخراج اسم الجدول
    const tableMatch = text.match(/الجدول.*?:\s*(\w+)/);
    if (tableMatch) {
      metadata.table_name = tableMatch[1];
    }
  } else if (text.includes('النية:') || text.includes('intent:')) {
    metadata.content_type = 'intent';
    
    // استخراج اسم النية
    const intentMatch = text.match(/intent\s*:\s*(\w+)/);
    if (intentMatch) {
      metadata.intent_name = intentMatch[1];
    }
  } else if (text.includes('أوصاف الأعمدة:')) {
    metadata.content_type = 'column_descriptions';
  } else if (text.includes('العلاقات بين الجداول:')) {
    metadata.content_type = 'table_relationships';
  } else if (text.includes('أمثلة تطبيقية:')) {
    metadata.content_type = 'examples';
  } else if (text.includes('الكيانات')) {
    metadata.content_type = 'entities';
  }

  // استخراج الجداول المذكورة
  const tableMatches = text.match(/tbltemp_\w+/g);
  if (tableMatches) {
    metadata.mentioned_tables = [...new Set(tableMatches)];
  }

  // استخراج الأعمدة المذكورة
  const columnMatches = text.match(/\b[A-Z][a-zA-Z]*(?:ID|Name|Date|Amount|Quantity|Price)\b/g);
  if (columnMatches) {
    metadata.mentioned_columns = [...new Set(columnMatches)];
  }

  return metadata;
}

async function setupVectorDatabase() {
  try {
    console.log('🚀 بدء إعداد Vector Database...');

    // تحميل Chroma
    const { ChromaClient } = await import('chromadb');

    // إنشاء Chroma client (in-memory)
    const client = new ChromaClient();

    console.log('✅ تم الاتصال بـ Chroma');

    // حذف collection إن وجدت
    try {
      await client.deleteCollection({ name: 'sql_knowledge_base' });
      console.log('🗑️ تم حذف collection القديمة');
    } catch (error) {
      console.log('ℹ️ لا توجد collection قديمة للحذف');
    }

    // إنشاء collection جديدة بدون embedding function (سنستخدم default)
    const collection = await client.createCollection({
      name: 'sql_knowledge_base',
      metadata: {
        description: 'قاعدة معرفة SQL للنظام التجاري',
        created_at: new Date().toISOString(),
        version: '1.0'
      }
    });

    console.log('✅ تم إنشاء collection جديدة');

    // قراءة ملف rga.txt
    const rgaPath = path.join(__dirname, '..', 'src', 'data', 'rga.txt');
    const rgaContent = await fs.readFile(rgaPath, 'utf-8');

    console.log(`📄 تم قراءة الملف: ${rgaContent.length} حرف`);

    // تقسيم النص إلى chunks
    const chunks = splitTextIntoChunks(rgaContent, 800);
    console.log(`🔪 تم تقسيم النص إلى ${chunks.length} قطعة`);

    // إعداد البيانات للفهرسة
    const documents = [];
    const metadatas = [];
    const ids = [];

    chunks.forEach((chunk, index) => {
      documents.push(chunk);
      metadatas.push(extractMetadata(chunk, index));
      ids.push(`chunk_${index}`);
    });

    // إضافة البيانات بدفعات صغيرة
    const batchSize = 10;
    for (let i = 0; i < documents.length; i += batchSize) {
      const batchDocs = documents.slice(i, i + batchSize);
      const batchMetas = metadatas.slice(i, i + batchSize);
      const batchIds = ids.slice(i, i + batchSize);

      await collection.add({
        documents: batchDocs,
        metadatas: batchMetas,
        ids: batchIds
      });

      console.log(`📝 تم فهرسة الدفعة ${Math.floor(i / batchSize) + 1}/${Math.ceil(documents.length / batchSize)}`);
    }

    // التحقق من النتائج
    const count = await collection.count();
    console.log(`✅ تم فهرسة ${count} عنصر بنجاح`);

    // اختبار البحث
    console.log('🔍 اختبار البحث...');
    const testResults = await collection.query({
      queryTexts: ['أكثر المنتجات مبيعاً'],
      nResults: 3
    });

    console.log(`📋 نتائج الاختبار: ${testResults.documents[0]?.length || 0} نتائج`);
    if (testResults.documents[0] && testResults.documents[0].length > 0) {
      console.log('📄 أول نتيجة:', testResults.documents[0][0].substring(0, 100) + '...');
    }

    console.log('🎉 تم إعداد Vector Database بنجاح!');

    // حفظ معلومات الإعداد
    const setupInfo = {
      setup_date: new Date().toISOString(),
      total_chunks: count,
      status: 'ready'
    };

    await fs.writeFile(
      path.join(__dirname, '..', 'src', 'data', 'vector-db-status.json'),
      JSON.stringify(setupInfo, null, 2)
    );

    console.log('💾 تم حفظ معلومات الإعداد');

  } catch (error) {
    console.error('❌ خطأ في إعداد Vector Database:', error);
    process.exit(1);
  }
}

// تشغيل الإعداد
if (require.main === module) {
  setupVectorDatabase();
}

module.exports = { setupVectorDatabase };
