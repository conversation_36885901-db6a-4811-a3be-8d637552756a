import { Chroma } from '@langchain/community/vectorstores/chroma';
import { RecursiveCharacterTextSplitter } from 'langchain/text_splitter';
import { Document } from 'langchain/document';
import fs from 'fs/promises';
import path from 'path';
import { spawn, ChildProcess } from 'child_process';

// نظام embeddings محلي بسيط
class LocalEmbeddings {
  // إنشاء embedding بسيط للنص
  async embedDocuments(texts: string[]): Promise<number[][]> {
    return texts.map(text => this.createEmbedding(text));
  }

  async embedQuery(text: string): Promise<number[]> {
    return this.createEmbedding(text);
  }

  private createEmbedding(text: string): number[] {
    const words = text.toLowerCase().split(/\s+/);
    const embedding = new Array(384).fill(0); // حجم ثابت للـ embedding
    
    // إنشاء embedding بناءً على الكلمات والمصطلحات المهمة
    const importantTerms = [
      'منتج', 'مبيعات', 'عميل', 'فاتورة', 'مخزون', 'كمية', 'سعر', 'مبلغ',
      'تاريخ', 'فرع', 'مورد', 'موزع', 'باركود', 'تصنيف', 'فئة',
      'ItemName', 'ClientName', 'Amount', 'Quantity', 'UnitPrice', 'TheDate',
      'BranchName', 'DocumentName', 'CategoryName', 'DistributorName',
      'tbltemp_ItemsMain', 'tbltemp_Inv_MainInvoice', 'الجدول', 'النية'
    ];

    // تسجيل نقاط للكلمات المهمة
    for (let i = 0; i < words.length; i++) {
      const word = words[i];
      
      // نقاط إضافية للمصطلحات المهمة
      const termIndex = importantTerms.findIndex(term => 
        word.includes(term.toLowerCase()) || term.toLowerCase().includes(word)
      );
      
      if (termIndex !== -1) {
        const index = (termIndex * 13) % embedding.length;
        embedding[index] += 3; // نقاط عالية للمصطلحات المهمة
      }
      
      // تسجيل عام للكلمات
      for (let j = 0; j < word.length; j++) {
        const charCode = word.charCodeAt(j);
        const index = (charCode + i + j) % embedding.length;
        embedding[index] += 1;
      }
    }
    
    // تطبيع الـ vector
    const magnitude = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
    return embedding.map(val => magnitude > 0 ? val / magnitude : 0);
  }
}

// إعداد Vector Store
let vectorStore: MemoryVectorStore | null = null;
let embeddings: LocalEmbeddings | null = null;

// تهيئة نظام RAG المحلي
export async function initializeLocalRAG(): Promise<void> {
  try {
    console.log('🚀 بدء تهيئة نظام RAG المحلي...');
    
    // إنشاء Local Embeddings
    embeddings = new LocalEmbeddings();
    
    // إنشاء Vector Store فارغ
    vectorStore = new MemoryVectorStore(embeddings);

    console.log('✅ تم تهيئة نظام RAG المحلي بنجاح');
  } catch (error) {
    console.error('❌ خطأ في تهيئة نظام RAG المحلي:', error);
    throw error;
  }
}

// استخراج metadata من النص
function extractMetadata(text: string, chunkIndex: number): Record<string, any> {
  const metadata: Record<string, any> = {
    chunk_index: chunkIndex,
    length: text.length,
    type: 'knowledge_base',
    embedding_type: 'local'
  };

  // استخراج نوع المحتوى
  if (text.includes('الجدول')) {
    metadata.content_type = 'table_description';
    
    // استخراج اسم الجدول
    const tableMatch = text.match(/الجدول.*?:\s*(\w+)/);
    if (tableMatch) {
      metadata.table_name = tableMatch[1];
    }
  } else if (text.includes('النية:') || text.includes('intent:')) {
    metadata.content_type = 'intent';
    
    // استخراج اسم النية
    const intentMatch = text.match(/intent\s*:\s*(\w+)/);
    if (intentMatch) {
      metadata.intent_name = intentMatch[1];
    }
  } else if (text.includes('أوصاف الأعمدة:')) {
    metadata.content_type = 'column_descriptions';
  } else if (text.includes('العلاقات بين الجداول:')) {
    metadata.content_type = 'table_relationships';
  } else if (text.includes('أمثلة تطبيقية:')) {
    metadata.content_type = 'examples';
  } else if (text.includes('الكيانات')) {
    metadata.content_type = 'entities';
  }

  // استخراج الجداول المذكورة
  const tableMatches = text.match(/tbltemp_\w+/g);
  if (tableMatches) {
    metadata.mentioned_tables = [...new Set(tableMatches)];
  }

  // استخراج الأعمدة المذكورة
  const columnMatches = text.match(/\b[A-Z][a-zA-Z]*(?:ID|Name|Date|Amount|Quantity|Price)\b/g);
  if (columnMatches) {
    metadata.mentioned_columns = [...new Set(columnMatches)];
  }

  return metadata;
}

// تحميل وفهرسة محتوى ملف rga.txt
export async function indexLocalKnowledgeBase(): Promise<void> {
  try {
    if (!vectorStore || !embeddings) {
      throw new Error('نظام RAG المحلي غير مهيأ');
    }

    console.log('📚 بدء فهرسة قاعدة المعرفة المحلية...');

    // قراءة ملف rga.txt
    const rgaPath = path.join(process.cwd(), 'src/data/rga.txt');
    const rgaContent = await fs.readFile(rgaPath, 'utf-8');

    console.log(`📄 تم قراءة الملف: ${rgaContent.length} حرف`);

    // إنشاء Text Splitter
    const textSplitter = new RecursiveCharacterTextSplitter({
      chunkSize: 1000,
      chunkOverlap: 200,
    });

    // تقسيم النص إلى chunks
    const chunks = await textSplitter.splitText(rgaContent);
    console.log(`🔪 تم تقسيم النص إلى ${chunks.length} قطعة`);

    // إنشاء Documents مع metadata
    const documents = chunks.map((chunk, index) => {
      const metadata = extractMetadata(chunk, index);
      return new Document({
        pageContent: chunk,
        metadata
      });
    });

    console.log('💾 إضافة Documents إلى Vector Store المحلي...');

    // إضافة Documents إلى Vector Store
    await vectorStore.addDocuments(documents);

    console.log(`✅ تم فهرسة ${chunks.length} قطعة من المعرفة بنجاح`);

    // حفظ معلومات الفهرسة
    const indexInfo = {
      indexed_at: new Date().toISOString(),
      total_chunks: chunks.length,
      embedding_type: 'local',
      status: 'ready'
    };

    await fs.writeFile(
      path.join(process.cwd(), 'src/data/local-rag-status.json'),
      JSON.stringify(indexInfo, null, 2)
    );

    console.log('💾 تم حفظ معلومات الفهرسة المحلية');

  } catch (error) {
    console.error('❌ خطأ في فهرسة قاعدة المعرفة المحلية:', error);
    throw error;
  }
}

// البحث في قاعدة المعرفة المحلية
export async function searchLocalKnowledge(
  query: string, 
  limit: number = 5
): Promise<any[]> {
  try {
    if (!vectorStore) {
      throw new Error('نظام RAG المحلي غير مهيأ');
    }

    console.log(`🔍 البحث المحلي: "${query}"`);

    // تنفيذ البحث
    const results = await vectorStore.similaritySearchWithScore(query, limit);

    console.log(`📋 تم العثور على ${results.length} نتائج`);

    // تنسيق النتائج
    const formattedResults = results.map((result, index) => ({
      content: result[0].pageContent,
      metadata: result[0].metadata,
      similarity: 1 - result[1], // تحويل المسافة إلى تشابه
      distance: result[1],
      id: `local_result_${index}`
    }));

    return formattedResults;
  } catch (error) {
    console.error('❌ خطأ في البحث المحلي:', error);
    return [];
  }
}

// الحصول على السياق المناسب للاستعلام (هذا ما سيرسل للـ LLM)
export async function getRelevantContextForLLM(query: string): Promise<string> {
  try {
    console.log('🎯 الحصول على السياق للنموذج اللغوي...');

    // البحث في أنواع مختلفة من المحتوى
    const [
      tableResults,
      intentResults,
      exampleResults,
      generalResults
    ] = await Promise.all([
      searchByContentType(query, 'table_description', 2),
      searchByContentType(query, 'intent', 2),
      searchByContentType(query, 'examples', 1),
      searchLocalKnowledge(query, 3)
    ]);

    // دمج النتائج وإزالة التكرار
    const allResults = [...tableResults, ...intentResults, ...exampleResults, ...generalResults];
    const uniqueResults = allResults.filter((result, index, self) => 
      index === self.findIndex(r => r.id === result.id)
    );

    // ترتيب حسب التشابه
    uniqueResults.sort((a, b) => b.similarity - a.similarity);

    // أخذ أفضل النتائج
    const topResults = uniqueResults.slice(0, 5);

    // تكوين السياق المنظم للـ LLM
    const context = topResults
      .map(result => {
        const type = result.metadata.content_type || 'معلومات عامة';
        return `[${type}]\n${result.content}`;
      })
      .join('\n\n---\n\n');

    console.log(`✅ تم تكوين سياق للـ LLM بطول ${context.length} حرف من ${topResults.length} مصادر`);

    return context;
  } catch (error) {
    console.error('❌ خطأ في الحصول على السياق للـ LLM:', error);
    return '';
  }
}

// البحث حسب نوع المحتوى
async function searchByContentType(query: string, contentType: string, limit: number = 3): Promise<any[]> {
  const allResults = await searchLocalKnowledge(query, 20);
  return allResults
    .filter(result => result.metadata.content_type === contentType)
    .slice(0, limit);
}

// إحصائيات النظام المحلي
export async function getLocalRAGStats(): Promise<any> {
  try {
    // قراءة معلومات الفهرسة
    try {
      const statusPath = path.join(process.cwd(), 'src/data/local-rag-status.json');
      const statusData = await fs.readFile(statusPath, 'utf-8');
      const status = JSON.parse(statusData);
      
      return {
        total_documents: status.total_chunks || 0,
        indexed_at: status.indexed_at,
        embedding_type: status.embedding_type,
        status: status.status || 'unknown',
        type: 'Local_RAG_with_LLM'
      };
    } catch (error) {
      return {
        total_documents: 0,
        status: 'not_indexed',
        type: 'Local_RAG_with_LLM'
      };
    }
  } catch (error) {
    console.error('❌ خطأ في الحصول على إحصائيات النظام المحلي:', error);
    return { error: 'فشل في الحصول على الإحصائيات' };
  }
}
