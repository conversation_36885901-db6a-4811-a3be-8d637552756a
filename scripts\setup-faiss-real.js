async function setupFaissReal() {
  try {
    console.log('🚀 بدء إعداد نظام FAISS Vector Database الحقيقي...');
    console.log('📊 سيتم تحويل ملف rga.txt إلى vectors وحفظها في قاعدة بيانات FAISS');

    // تحميل خدمة FAISS
    const { 
      initializeFaissRAG, 
      indexFaissKnowledgeBase, 
      searchFaissKnowledge, 
      getFaissStats,
      loadFaissIndex
    } = await import('../src/lib/faiss-rag-service.js');

    console.log('✅ تم تحميل خدمة FAISS RAG');

    // 1. تهيئة نظام FAISS
    console.log('\n1️⃣ تهيئة FAISS Vector Database...');
    await initializeFaissRAG();

    // 2. فهرسة قاعدة المعرفة وتحويلها إلى vectors
    console.log('\n2️⃣ تحويل rga.txt إلى vectors وفهرسة في FAISS...');
    console.log('⏳ هذه العملية قد تستغرق بعض الوقت...');
    
    const startTime = Date.now();
    await indexFaissKnowledgeBase();
    const indexTime = Date.now() - startTime;
    
    console.log(`⏱️ تم الانتهاء من الفهرسة في ${indexTime} مللي ثانية`);

    // 3. التحقق من حفظ قاعدة البيانات
    console.log('\n3️⃣ التحقق من قاعدة البيانات المحفوظة...');
    const fs = require('fs').promises;
    const path = require('path');
    
    const dbPath = path.join(process.cwd(), 'faiss_database');
    try {
      const files = await fs.readdir(dbPath);
      console.log(`📁 ملفات قاعدة البيانات: ${files.length} ملف`);
      console.log(`📋 الملفات: ${files.join(', ')}`);
      
      // حساب حجم قاعدة البيانات
      let totalSize = 0;
      for (const file of files) {
        const filePath = path.join(dbPath, file);
        const stats = await fs.stat(filePath);
        totalSize += stats.size;
        console.log(`   📄 ${file}: ${(stats.size / 1024).toFixed(2)} KB`);
      }
      console.log(`📊 إجمالي حجم قاعدة البيانات: ${(totalSize / 1024).toFixed(2)} KB`);
    } catch (error) {
      console.log('❌ خطأ في قراءة ملفات قاعدة البيانات:', error.message);
    }

    // 4. الحصول على الإحصائيات
    console.log('\n4️⃣ الحصول على إحصائيات FAISS...');
    const stats = await getFaissStats();
    console.log('📊 إحصائيات FAISS Vector Database:');
    console.log(JSON.stringify(stats, null, 2));

    // 5. اختبار تحميل قاعدة البيانات
    console.log('\n5️⃣ اختبار تحميل قاعدة البيانات من القرص...');
    try {
      await loadFaissIndex();
      console.log('✅ تم تحميل قاعدة البيانات بنجاح من القرص');
    } catch (error) {
      console.log('❌ خطأ في تحميل قاعدة البيانات:', error.message);
    }

    // 6. اختبار البحث المتقدم
    console.log('\n6️⃣ اختبار البحث في FAISS Vector Database...');
    
    const testQueries = [
      'أكثر المنتجات مبيعاً',
      'تفاصيل جدول المنتجات',
      'العلاقات بين الجداول',
      'أمثلة استعلامات SQL',
      'مبيعات الفرع الرئيسي',
      'تحليل العملاء',
      'مخزون المنتجات'
    ];

    for (const query of testQueries) {
      console.log(`\n🔍 اختبار: "${query}"`);
      const searchStart = Date.now();
      const results = await searchFaissKnowledge(query, 3, 0.1);
      const searchTime = Date.now() - searchStart;
      
      if (results.length > 0) {
        console.log(`   ✅ تم العثور على ${results.length} نتائج في ${searchTime} مللي ثانية`);
        console.log(`   📊 أعلى تشابه: ${(results[0].similarity * 100).toFixed(1)}%`);
        console.log(`   📄 نوع المحتوى: ${results[0].metadata.content_type || 'غير محدد'}`);
        console.log(`   📏 نقاط التشابه: ${results[0].score?.toFixed(4) || 'غير محدد'}`);
        console.log(`   📝 بداية المحتوى: ${results[0].content.substring(0, 80)}...`);
        
        // عرض metadata إضافية
        if (results[0].metadata.mentioned_tables) {
          console.log(`   🗂️ الجداول المذكورة: ${results[0].metadata.mentioned_tables.join(', ')}`);
        }
        if (results[0].metadata.keywords) {
          console.log(`   🔑 الكلمات المفتاحية: ${results[0].metadata.keywords.slice(0, 3).join(', ')}`);
        }
        if (results[0].metadata.importance_score) {
          console.log(`   ⭐ درجة الأهمية: ${results[0].metadata.importance_score.toFixed(2)}`);
        }
      } else {
        console.log(`   ❌ لم يتم العثور على نتائج`);
      }
    }

    // 7. اختبار الأداء
    console.log('\n7️⃣ اختبار أداء FAISS Vector Database...');
    const performanceQuery = 'أكثر المنتجات مبيعاً';
    const iterations = 5;
    const times = [];

    for (let i = 0; i < iterations; i++) {
      const startTime = Date.now();
      await searchFaissKnowledge(performanceQuery, 5, 0.1);
      const endTime = Date.now();
      times.push(endTime - startTime);
      console.log(`   ⏱️ التكرار ${i + 1}: ${endTime - startTime} مللي ثانية`);
    }

    const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);
    
    console.log(`   📊 متوسط الوقت: ${avgTime.toFixed(2)} مللي ثانية`);
    console.log(`   📊 أسرع بحث: ${minTime} مللي ثانية`);
    console.log(`   📊 أبطأ بحث: ${maxTime} مللي ثانية`);

    // 8. اختبار جودة النتائج
    console.log('\n8️⃣ اختبار جودة النتائج...');
    const qualityTests = [
      {
        query: 'جدول المنتجات الرئيسي',
        expectedType: 'table_description',
        expectedKeywords: ['tbltemp_ItemsMain', 'ItemName'],
        minSimilarity: 0.3
      },
      {
        query: 'مقارنة مبيعات الفروع',
        expectedType: 'intent',
        expectedKeywords: ['مقارنة', 'مبيعات', 'فرع'],
        minSimilarity: 0.2
      },
      {
        query: 'استعلام SQL للعملاء',
        expectedKeywords: ['SQL', 'عميل', 'استعلام'],
        minSimilarity: 0.2
      }
    ];

    for (const test of qualityTests) {
      console.log(`\n🎯 اختبار جودة: "${test.query}"`);
      const results = await searchFaissKnowledge(test.query, 3, 0.1);
      
      if (results.length > 0) {
        const topResult = results[0];
        
        // اختبار نوع المحتوى
        if (test.expectedType) {
          if (topResult.metadata.content_type === test.expectedType) {
            console.log(`   ✅ نوع المحتوى صحيح: ${test.expectedType}`);
          } else {
            console.log(`   ⚠️ نوع المحتوى غير متوقع: ${topResult.metadata.content_type}`);
          }
        }
        
        // اختبار الكلمات المفتاحية
        const hasKeywords = test.expectedKeywords.some(keyword => 
          topResult.content.toLowerCase().includes(keyword.toLowerCase())
        );
        
        if (hasKeywords) {
          console.log(`   ✅ يحتوي على الكلمات المفتاحية المتوقعة`);
        } else {
          console.log(`   ⚠️ لا يحتوي على الكلمات المفتاحية المتوقعة`);
        }
        
        // اختبار التشابه
        if (topResult.similarity >= test.minSimilarity) {
          console.log(`   ✅ نقاط التشابه مقبولة: ${(topResult.similarity * 100).toFixed(1)}%`);
        } else {
          console.log(`   ⚠️ نقاط التشابه منخفضة: ${(topResult.similarity * 100).toFixed(1)}%`);
        }
      } else {
        console.log(`   ❌ لم يتم العثور على نتائج`);
      }
    }

    // 9. ملخص النتائج النهائي
    console.log('\n9️⃣ ملخص الإعداد النهائي...');
    console.log('🎉 تم إعداد FAISS Vector Database بنجاح!');
    console.log('📋 الملخص الشامل:');
    console.log(`   - إجمالي المستندات: ${stats.total_documents}`);
    console.log(`   - نوع Embedding: ${stats.embedding_type || 'faiss_advanced'}`);
    console.log(`   - أبعاد Embedding: ${stats.embedding_dimension || 512}`);
    console.log(`   - مسار قاعدة البيانات: ${stats.database_path || './faiss_database'}`);
    console.log(`   - مصدر البيانات: ${stats.file_source || 'rga.txt'}`);
    console.log(`   - حجم الملف الأصلي: ${stats.file_size ? (stats.file_size / 1024).toFixed(2) + ' KB' : 'غير محدد'}`);
    console.log(`   - متوسط وقت البحث: ${avgTime.toFixed(2)} مللي ثانية`);
    console.log(`   - حالة النظام: ${stats.status}`);
    console.log(`   - ملفات قاعدة البيانات موجودة: ${stats.files_exist ? 'نعم' : 'لا'}`);

    console.log('\n✅ نظام FAISS Vector Database جاهز للاستخدام!');
    console.log('📁 قاعدة البيانات محفوظة بشكل دائم في مجلد: ./faiss_database');
    console.log('🔗 يمكن الآن ربطه مع النموذج اللغوي لتوليد استعلامات SQL بدقة عالية');
    console.log('🚀 النظام يدعم البحث السريع والدقيق في المعرفة المفهرسة');

  } catch (error) {
    console.error('❌ خطأ في إعداد FAISS Vector Database:', error);
    console.error('📋 تفاصيل الخطأ:', error.stack);
    process.exit(1);
  }
}

// تشغيل الإعداد
if (require.main === module) {
  setupFaissReal();
}

module.exports = { setupFaissReal };
