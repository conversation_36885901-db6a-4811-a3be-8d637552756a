import { Chroma } from '@langchain/community/vectorstores/chroma';
import { RecursiveCharacterTextSplitter } from 'langchain/text_splitter';
import { Document } from 'langchain/document';
import fs from 'fs/promises';
import path from 'path';
import { spawn, ChildProcess } from 'child_process';

// نظام embeddings محلي بسيط متوافق مع LangChain
class LocalEmbeddings {
  // إنشاء embedding بسيط للنص
  async embedDocuments(texts: string[]): Promise<number[][]> {
    return texts.map(text => this.createEmbedding(text));
  }

  async embedQuery(text: string): Promise<number[]> {
    return this.createEmbedding(text);
  }

  private createEmbedding(text: string): number[] {
    const words = text.toLowerCase().split(/\s+/);
    const embedding = new Array(384).fill(0); // حجم ثابت للـ embedding
    
    // إنشاء embedding بناءً على الكلمات والمصطلحات المهمة
    const importantTerms = [
      'منتج', 'مبيعات', 'عميل', 'فاتورة', 'مخزون', 'كمية', 'سعر', 'مبلغ',
      'تاريخ', 'فرع', 'مورد', 'موزع', 'باركود', 'تصنيف', 'فئة',
      'ItemName', 'ClientName', 'Amount', 'Quantity', 'UnitPrice', 'TheDate',
      'BranchName', 'DocumentName', 'CategoryName', 'DistributorName',
      'tbltemp_ItemsMain', 'tbltemp_Inv_MainInvoice', 'الجدول', 'النية'
    ];

    // تسجيل نقاط للكلمات المهمة
    for (let i = 0; i < words.length; i++) {
      const word = words[i];
      
      // نقاط إضافية للمصطلحات المهمة
      const termIndex = importantTerms.findIndex(term => 
        word.includes(term.toLowerCase()) || term.toLowerCase().includes(word)
      );
      
      if (termIndex !== -1) {
        const index = (termIndex * 13) % embedding.length;
        embedding[index] += 3; // نقاط عالية للمصطلحات المهمة
      }
      
      // تسجيل عام للكلمات
      for (let j = 0; j < word.length; j++) {
        const charCode = word.charCodeAt(j);
        const index = (charCode + i + j) % embedding.length;
        embedding[index] += 1;
      }
    }
    
    // تطبيع الـ vector
    const magnitude = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
    return embedding.map(val => magnitude > 0 ? val / magnitude : 0);
  }
}

// إعداد Chroma Vector Store
let chromaVectorStore: Chroma | null = null;
let embeddings: LocalEmbeddings | null = null;
let chromaServer: ChildProcess | null = null;

// بدء خادم Chroma المحلي
async function startChromaServer(): Promise<void> {
  return new Promise((resolve, reject) => {
    console.log('🚀 بدء تشغيل خادم Chroma المحلي...');
    
    // إنشاء مجلد قاعدة البيانات
    const dbPath = path.join(process.cwd(), 'chroma_database');
    
    // تشغيل خادم Chroma
    chromaServer = spawn('python', ['-m', 'chromadb.cli.cli', 'run', '--host', 'localhost', '--port', '8000', '--path', dbPath], {
      stdio: ['ignore', 'pipe', 'pipe']
    });

    chromaServer.stdout?.on('data', (data) => {
      const output = data.toString();
      console.log('Chroma Server:', output);
      if (output.includes('Running on')) {
        console.log('✅ خادم Chroma يعمل على http://localhost:8000');
        resolve();
      }
    });

    chromaServer.stderr?.on('data', (data) => {
      console.error('Chroma Server Error:', data.toString());
    });

    chromaServer.on('error', (error) => {
      console.error('❌ فشل في تشغيل خادم Chroma:', error);
      // محاولة بديلة بدون Python
      console.log('🔄 محاولة تشغيل Chroma بطريقة أخرى...');
      resolve(); // نتابع بدون خادم منفصل
    });

    // انتظار 3 ثوانٍ ثم المتابعة
    setTimeout(() => {
      resolve();
    }, 3000);
  });
}

// تهيئة نظام Chroma Vector Database
export async function initializeChromaVector(): Promise<void> {
  try {
    console.log('🚀 بدء تهيئة Chroma Vector Database...');
    
    // بدء خادم Chroma (اختياري)
    await startChromaServer();
    
    // إنشاء Local Embeddings
    embeddings = new LocalEmbeddings();
    
    // إنشاء مجلد قاعدة البيانات
    const dbPath = path.join(process.cwd(), 'chroma_database');
    try {
      await fs.access(dbPath);
    } catch {
      await fs.mkdir(dbPath, { recursive: true });
      console.log('📁 تم إنشاء مجلد قاعدة البيانات:', dbPath);
    }

    // إنشاء Chroma Vector Store
    chromaVectorStore = new Chroma(embeddings, {
      collectionName: 'sql_knowledge_vectors',
      url: 'http://localhost:8000', // خادم Chroma المحلي
      collectionMetadata: {
        description: 'قاعدة بيانات Vector للمعرفة SQL',
        created_at: new Date().toISOString(),
        version: '2.0'
      }
    });

    console.log('✅ تم تهيئة Chroma Vector Database بنجاح');
  } catch (error) {
    console.error('❌ خطأ في تهيئة Chroma Vector Database:', error);
    
    // fallback إلى نظام محلي بسيط
    console.log('🔄 التبديل إلى نظام Vector محلي بسيط...');
    const { MemoryVectorStore } = await import('langchain/vectorstores/memory');
    chromaVectorStore = new MemoryVectorStore(embeddings!) as any;
    console.log('✅ تم تهيئة نظام Vector محلي بديل');
  }
}

// استخراج metadata من النص
function extractMetadata(text: string, chunkIndex: number): Record<string, any> {
  const metadata: Record<string, any> = {
    chunk_index: chunkIndex,
    length: text.length,
    type: 'knowledge_base',
    embedding_type: 'chroma_local'
  };

  // استخراج نوع المحتوى
  if (text.includes('الجدول')) {
    metadata.content_type = 'table_description';
    
    // استخراج اسم الجدول
    const tableMatch = text.match(/الجدول.*?:\s*(\w+)/);
    if (tableMatch) {
      metadata.table_name = tableMatch[1];
    }
  } else if (text.includes('النية:') || text.includes('intent:')) {
    metadata.content_type = 'intent';
    
    // استخراج اسم النية
    const intentMatch = text.match(/intent\s*:\s*(\w+)/);
    if (intentMatch) {
      metadata.intent_name = intentMatch[1];
    }
  } else if (text.includes('أوصاف الأعمدة:')) {
    metadata.content_type = 'column_descriptions';
  } else if (text.includes('العلاقات بين الجداول:')) {
    metadata.content_type = 'table_relationships';
  } else if (text.includes('أمثلة تطبيقية:')) {
    metadata.content_type = 'examples';
  } else if (text.includes('الكيانات')) {
    metadata.content_type = 'entities';
  }

  // استخراج الجداول المذكورة
  const tableMatches = text.match(/tbltemp_\w+/g);
  if (tableMatches) {
    metadata.mentioned_tables = [...new Set(tableMatches)];
  }

  // استخراج الأعمدة المذكورة
  const columnMatches = text.match(/\b[A-Z][a-zA-Z]*(?:ID|Name|Date|Amount|Quantity|Price)\b/g);
  if (columnMatches) {
    metadata.mentioned_columns = [...new Set(columnMatches)];
  }

  return metadata;
}

// تحميل وفهرسة محتوى ملف rga.txt في Chroma
export async function indexChromaKnowledgeBase(): Promise<void> {
  try {
    if (!chromaVectorStore || !embeddings) {
      throw new Error('Chroma Vector Database غير مهيأ');
    }

    console.log('📚 بدء فهرسة قاعدة المعرفة في Chroma...');

    // قراءة ملف rga.txt
    const rgaPath = path.join(process.cwd(), 'src/data/rga.txt');
    const rgaContent = await fs.readFile(rgaPath, 'utf-8');

    console.log(`📄 تم قراءة الملف: ${rgaContent.length} حرف`);

    // إنشاء Text Splitter
    const textSplitter = new RecursiveCharacterTextSplitter({
      chunkSize: 1000,
      chunkOverlap: 200,
    });

    // تقسيم النص إلى chunks
    const chunks = await textSplitter.splitText(rgaContent);
    console.log(`🔪 تم تقسيم النص إلى ${chunks.length} قطعة`);

    // إنشاء Documents مع metadata
    const documents = chunks.map((chunk, index) => {
      const metadata = extractMetadata(chunk, index);
      return new Document({
        pageContent: chunk,
        metadata
      });
    });

    console.log('💾 إضافة Documents إلى Chroma Vector Store...');

    // إضافة Documents إلى Chroma
    await chromaVectorStore.addDocuments(documents);

    console.log(`✅ تم فهرسة ${chunks.length} قطعة في Chroma بنجاح`);

    // حفظ معلومات الفهرسة
    const indexInfo = {
      indexed_at: new Date().toISOString(),
      total_chunks: chunks.length,
      embedding_type: 'chroma_local',
      database_path: path.join(process.cwd(), 'chroma_database'),
      status: 'ready'
    };

    await fs.writeFile(
      path.join(process.cwd(), 'src/data/chroma-vector-status.json'),
      JSON.stringify(indexInfo, null, 2)
    );

    console.log('💾 تم حفظ معلومات فهرسة Chroma');

  } catch (error) {
    console.error('❌ خطأ في فهرسة قاعدة المعرفة في Chroma:', error);
    throw error;
  }
}

// البحث في Chroma Vector Store
export async function searchChromaKnowledge(
  query: string, 
  limit: number = 5
): Promise<any[]> {
  try {
    if (!chromaVectorStore) {
      throw new Error('Chroma Vector Database غير مهيأ');
    }

    console.log(`🔍 البحث في Chroma: "${query}"`);

    // تنفيذ البحث
    const results = await chromaVectorStore.similaritySearchWithScore(query, limit);

    console.log(`📋 تم العثور على ${results.length} نتائج في Chroma`);

    // تنسيق النتائج
    const formattedResults = results.map((result, index) => ({
      content: result[0].pageContent,
      metadata: result[0].metadata,
      similarity: 1 - result[1], // تحويل المسافة إلى تشابه
      distance: result[1],
      id: `chroma_result_${index}`
    }));

    return formattedResults;
  } catch (error) {
    console.error('❌ خطأ في البحث في Chroma:', error);
    return [];
  }
}

// الحصول على السياق المناسب للنموذج اللغوي من Chroma
export async function getChromaContextForLLM(query: string): Promise<string> {
  try {
    console.log('🎯 الحصول على السياق من Chroma للنموذج اللغوي...');

    // البحث في أنواع مختلفة من المحتوى
    const results = await searchChromaKnowledge(query, 5);

    // تكوين السياق المنظم للـ LLM
    const context = results
      .map(result => {
        const type = result.metadata.content_type || 'معلومات عامة';
        return `[${type}]\n${result.content}`;
      })
      .join('\n\n---\n\n');

    console.log(`✅ تم تكوين سياق Chroma للـ LLM بطول ${context.length} حرف من ${results.length} مصادر`);

    return context;
  } catch (error) {
    console.error('❌ خطأ في الحصول على السياق من Chroma للـ LLM:', error);
    return '';
  }
}

// إحصائيات Chroma Vector Database
export async function getChromaVectorStats(): Promise<any> {
  try {
    // قراءة معلومات الفهرسة
    try {
      const statusPath = path.join(process.cwd(), 'src/data/chroma-vector-status.json');
      const statusData = await fs.readFile(statusPath, 'utf-8');
      const status = JSON.parse(statusData);
      
      return {
        total_documents: status.total_chunks || 0,
        indexed_at: status.indexed_at,
        embedding_type: status.embedding_type,
        database_path: status.database_path,
        status: status.status || 'unknown',
        type: 'Chroma_Vector_Database'
      };
    } catch (error) {
      return {
        total_documents: 0,
        status: 'not_indexed',
        type: 'Chroma_Vector_Database'
      };
    }
  } catch (error) {
    console.error('❌ خطأ في الحصول على إحصائيات Chroma Vector:', error);
    return { error: 'فشل في الحصول على الإحصائيات' };
  }
}

// إيقاف خادم Chroma
export async function stopChromaServer(): Promise<void> {
  if (chromaServer) {
    console.log('🛑 إيقاف خادم Chroma...');
    chromaServer.kill();
    chromaServer = null;
    console.log('✅ تم إيقاف خادم Chroma');
  }
}
